import { useState, useEffect } from "react";
import { CleanProgressBar } from "./CleanProgressBar";
import { ZipCodeStep } from "./steps/ZipCodeStep";
import { LocationStep } from "./steps/LocationStep";
import { ContactInfoStep } from "./steps/ContactInfoStep";
import { ServiceStep } from "./steps/ServiceStep";
import { ScheduleStep } from "./steps/ScheduleStep";
import { ConfirmationStep } from "./steps/ConfirmationStep";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import { CheckCircle } from "lucide-react";
import { useReward } from "react-rewards";

interface BookingData {
  zipCode?: string;
  service?: string;
  description?: string;
  date?: Date;
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  houseNumber?: string;
  street?: string;
  city?: string;
  notes?: string;
}

const STEPS = [
  "Zip Code",
  "Contact Info",
  "Address",
  "Service",
  "Schedule",
  "Confirm",
];

export function FullPageBooking() {
  const companyConfig = useCompanyConfig();
  const {
    bookingData,
    updateBookingData,
    clearBookingData,
    getDateAsObject,
    setDateFromObject,
  } = useBookingFormStorage();

  const { reward, isAnimating } = useReward("fullpage-reward-id", "confetti", {
    elementCount: 200,
    startVelocity: 30,
    lifetime: 200,
  });

  const [currentStep, setCurrentStep] = useState(bookingData.currentStep || 1);
  const [isComplete, setIsComplete] = useState(false);

  const goToNextStep = (stepData: Partial<BookingData>) => {
    const dataToStore = { ...stepData };
    if (dataToStore.date) {
      setDateFromObject(dataToStore.date);
    }

    const nextStep = currentStep + 1;
    updateBookingData({ ...dataToStore, currentStep: nextStep });
    if (currentStep < STEPS.length) {
      setCurrentStep(nextStep);
    }
  };

  const handleLocationStepNext = (
    data: { street: string; city: string; state: string },
    zipCode: string
  ) => {
    goToNextStep({ ...data, zipCode });
  };

  const handleEditStep = (step: string) => {
    switch (step) {
      case "start":
        setCurrentStep(1);
        break;
      case "service":
        setCurrentStep(4);
        break;
      case "schedule":
        setCurrentStep(5);
        break;
      case "contact":
        setCurrentStep(2);
        break;
      case "location":
        setCurrentStep(3);
        break;
      default:
        break;
    }
  };

  const handleConfirmBooking = () => {
    setIsComplete(true);
    reward(); // Trigger confetti

    toast({
      title: "Booking Confirmed!",
      description:
        "We'll be in touch shortly to confirm your appointment details.",
    });
  };

  const handleStartNewBooking = () => {
    clearBookingData();
    setCurrentStep(1);
    setIsComplete(false);
  };

  if (isComplete) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center p-4">
        <div className="w-full max-w-md text-center">
          <span id="fullpage-reward-id" className="absolute" />
          <div className="w-20 h-20 bg-booking-step-complete rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Booking Confirmed!
          </h1>
          <p className="text-muted-foreground mb-8">
            Thank you for choosing {companyConfig.name}. We'll be in touch
            shortly to confirm your appointment details.
          </p>
          <button
            onClick={handleStartNewBooking}
            className="w-full bg-gradient-primary text-white py-3 px-6 rounded-lg font-medium hover:opacity-90 transition-opacity"
          >
            Book Another Appointment
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-6 overflow-y-auto">
        {/* Header with Company Branding */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center gap-3 mb-4">
            {companyConfig.logo ? (
              <img
                src={companyConfig.logo}
                alt={`${companyConfig.name} Logo`}
                className="w-12 h-12 object-contain"
              />
            ) : (
              <div className="w-12 h-12 bg-gradient-to-r from-primary to-primary-light rounded-full flex items-center justify-center">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
                  />
                </svg>
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary-light bg-clip-text text-transparent">
                {companyConfig.name}
              </h1>
              <p className="text-sm text-muted-foreground">Book Service</p>
            </div>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="max-w-4xl mx-auto mb-8">
          <CleanProgressBar
            currentStep={currentStep}
            totalSteps={STEPS.length}
            steps={STEPS}
            showStepNames={true}
          />
        </div>

        {/* Form Steps */}
        <div className="max-w-4xl mx-auto bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-6">
          {/* Step 1: Zip Code */}
          {currentStep === 1 && (
            <ZipCodeStep
              onComplete={goToNextStep}
              initialData={{ zipCode: bookingData.zipCode || "" }}
            />
          )}

          {/* Step 2: Contact Info */}
          {currentStep === 2 && (
            <ContactInfoStep
              onNext={goToNextStep}
              initialData={{
                firstName: bookingData.firstName,
                lastName: bookingData.lastName,
                email: bookingData.email,
                phone: bookingData.phone,
              }}
            />
          )}

          {/* Step 3: Location */}
          {currentStep === 3 && bookingData.zipCode && (
            <LocationStep
              onNext={handleLocationStepNext}
              zipCode={bookingData.zipCode}
              initialData={{
                houseNumber: bookingData.houseNumber || "",
                street: bookingData.street || "",
                city: bookingData.city || "",
              }}
            />
          )}

          {/* Step 4: Service */}
          {currentStep === 4 && (
            <ServiceStep
              onNext={goToNextStep}
              initialData={{
                service: bookingData.service || "",
                description: bookingData.description || "",
              }}
            />
          )}

          {/* Step 5: Schedule */}
          {currentStep === 5 && (
            <ScheduleStep
              onNext={goToNextStep}
              initialData={{
                date: getDateAsObject(),
                time: bookingData.time || "",
              }}
            />
          )}

          {/* Step 6: Confirmation */}
          {currentStep === 6 && (
            <ConfirmationStep
              data={
                {
                  ...bookingData,
                  date: getDateAsObject() || new Date(),
                } as any
              }
              onConfirm={handleConfirmBooking}
              onEdit={handleEditStep}
            />
          )}
        </div>
      </div>
    </div>
  );
  console.log("FullPageBooking: bookingData before ConfirmationStep", bookingData);
}
