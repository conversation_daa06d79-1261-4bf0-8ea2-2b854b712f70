import React from "react";
import { ConfirmationStep } from "./steps/ConfirmationStep";
import { KnownContactInfoStep } from "./steps/KnownContactInfoStep";

// Chat conversation steps - natural flow, one field at a time
export enum KnownChatStep {
  WELCOME = "welcome",
  CONFIRM_CONTACT = "confirmContact",
  SERVICE_TYPE = "serviceType",
  SERVICE_CATEGORY = "serviceCategory",
  SERVICE_SPECIFIC = "serviceSpecific",
  SERVICE_DESCRIPTION = "serviceDescription",
  SCHEDULE_DATE = "scheduleDate",
  SCHEDULE_TIME = "scheduleTime",
  CONFIRMATION = "confirmation",
  COMPLETE = "complete",
}

export interface ChatFlowReturn {
  content?: string | React.ReactNode;
  options?: string[];
  inputType?: "text" | "email" | "tel" | "date" | "time";
  placeholder?: string;
  validation?: (value: string) => string | null;
}

import { services } from "@/config/company";
import { format } from "date-fns";

// Validation functions
export const validators = {
  name: (value: string) => {
    if (!value.trim()) return "Please enter your full name";
    if (value.trim().length < 3)
      return "Name must be at least 3 characters";
    return null;
  },

  email: (value: string) => {
    if (!value.trim()) return "Please enter your email address";
    if (!/^\S+@\S+\.\S+$/.test(value.trim()))
      return "Please enter a valid email address";
    return null;
  },

  phone: (value: string) => {
    if (!value.trim()) return "Please enter your phone number";
    const cleaned = value.replace(/\D/g, "");
    if (cleaned.length < 10) return "Please enter a valid phone number";
    return null;
  },

  address: (value: string) => {
    if (!value.trim()) return "Please enter your address";
    if (value.trim().length < 10) return "Please enter a complete address";
    return null;
  },
};

// Helper to generate available time slots for chat
const generateChatAvailableSlots = () => {
  const slots = [];
  const timeSlots = [
    "08:00 - 10:00",
    "10:00 - 12:00",
    "12:00 - 14:00",
    "14:00 - 16:00",
    "16:00 - 18:00",
  ];
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let i = 0; i < 5; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + 1 + i); // Start from tomorrow
    const timeRange = timeSlots[i % timeSlots.length]; // Cycle through time slots
    slots.push(`${format(date, "EEE, MMM do, yyyy")}, ${timeRange}`);
  }
  return slots;
};

// Chat flow messages and logic
export const getKnownChatFlow = (
  step: KnownChatStep,
  data?: any
): ChatFlowReturn => {
  switch (step) {
    case KnownChatStep.WELCOME:
      return {
        content: `Hi ${data?.name || "there"}! 👋 Welcome back to ${ 
          data?.companyName || "our service" 
        }! I'm here to help you book your service appointment. This will only take a few minutes, and I'll guide you through each step. Ready to get started?`,
        options: ["Yes, let's start!", "Tell me more first"],
      };

    case KnownChatStep.CONFIRM_CONTACT:
      return {
        content: null, // This will be replaced by a React component
      };

    case KnownChatStep.SERVICE_TYPE:
      return {
        content:
          "Now for the fun part! What type of service do you need? I can help with all of these:",
        options: Object.keys(services),
      };

    case KnownChatStep.SERVICE_CATEGORY:
      const currentService = 
        data?.service?.split(" - ")[0] || data?.selectedService; // Get the main service
      const serviceTypes = services[currentService as keyof typeof services];
      return {
        content: `Great choice! For ${currentService}, what type of work do you need?`,
        options: Object.keys(serviceTypes || {}),
      };

    case KnownChatStep.SERVICE_SPECIFIC:
      const mainService = 
        data?.service?.split(" - ")[0] || data?.selectedService;
      const subService = 
        data?.service?.split(" - ")[1] || data?.selectedSubService;

      const specificServices = 
        services[mainService as keyof typeof services]?.[subService as keyof (typeof services)[keyof typeof services]];
      if (Array.isArray(specificServices) && specificServices.length > 0) {
        return {
          content: `Perfect! What specifically needs to be ${subService?.toLowerCase()}?`,
          options: specificServices,
        };
      } else {
        // Skip to description if no specific options
        return getKnownChatFlow(KnownChatStep.SERVICE_DESCRIPTION, data);
      }

    case KnownChatStep.SERVICE_DESCRIPTION:
      return {
        content:
          "Got it! Is there anything specific about your issue you'd like me to know? This helps our technician come prepared with the right tools and parts. (Optional - you can skip this)",
        inputType: "text" as const,
        placeholder: "Describe your issue (optional)",
        validation: () => null, // Optional field
        options: ["Skip"], // Added Skip option
      };

    case KnownChatStep.SCHEDULE_DATE:
      const availableSlots = generateChatAvailableSlots();
      return {
        content:
          "Almost done! When would you like to schedule your appointment? Here are some first available options:",
        options: availableSlots,
        validation: (value: string) => {
          if (!value.trim()) return "Please select an appointment slot.";
          if (!availableSlots.includes(value))
            return "Please select a valid appointment slot from the options.";
          return null;
        },
      };

    case KnownChatStep.SCHEDULE_TIME:
      return {
        content: "What time works best for you?",
        options: [
          "8:00 AM - 10:00 AM",
          "10:00 AM - 12:00 PM",
          "12:00 PM - 2:00 PM",
          "2:00 PM - 4:00 PM",
          "4:00 PM - 6:00 PM",
        ],
      };

    case KnownChatStep.CONFIRMATION:
      return {
        content: null, // This will be replaced by a React component
        options: ["Yes, book it!", "Let me make changes"],
      };

    case KnownChatStep.COMPLETE:
      return {
        content: `🎉 Awesome! Your appointment has been booked successfully! \n\nYou'll receive a confirmation email shortly with all the details. Our technician will call you when they're on their way.\n\nThank you for choosing ${data?.companyName}! Is there anything else I can help you with?`,
        options: ["Book another service", "That's all, thanks!"],
      };

    default:
      return {
        content: "I'm not sure how to help with that. Let me start over.",
        options: ["Start booking process"],
      };
  }
};

// Helper function to get next step
export const getNextKnownStep = (
  currentStep: KnownChatStep,
  response: string,
  data?: any
): KnownChatStep => {
  switch (currentStep) {
    case KnownChatStep.WELCOME:
      if (response.includes("start")) return KnownChatStep.CONFIRM_CONTACT;
      return KnownChatStep.CONFIRM_CONTACT; // Always proceed to confirm contact

    case KnownChatStep.CONFIRM_CONTACT:
      return KnownChatStep.SERVICE_TYPE;

    case KnownChatStep.SERVICE_TYPE:
      return KnownChatStep.SERVICE_CATEGORY;

    case KnownChatStep.SERVICE_CATEGORY:
      // Check if there are specific services available
      const mainService = 
        data?.service?.split(" - ")[0] || data?.selectedService;
      const subServiceResponse = response; // The response is the selected sub-service

      const serviceTypes = services[mainService as keyof typeof services];
      const specificServices = 
        serviceTypes?.[subServiceResponse as keyof typeof serviceTypes];
      if (Array.isArray(specificServices) && specificServices.length > 0) {
        return KnownChatStep.SERVICE_SPECIFIC;
      }
      return KnownChatStep.SERVICE_DESCRIPTION;

    case KnownChatStep.SERVICE_SPECIFIC:
      return KnownChatStep.SERVICE_DESCRIPTION;

    case KnownChatStep.SERVICE_DESCRIPTION:
      return KnownChatStep.SCHEDULE_DATE;

    case KnownChatStep.SCHEDULE_DATE:
      return KnownChatStep.CONFIRMATION;

    case KnownChatStep.SCHEDULE_TIME:
      return KnownChatStep.CONFIRMATION;

    case KnownChatStep.CONFIRMATION:
      if (response.includes("book")) return KnownChatStep.COMPLETE;
      return KnownChatStep.CONFIRM_CONTACT; // Start over for changes

    case KnownChatStep.COMPLETE:
      if (response.includes("another")) return KnownChatStep.CONFIRM_CONTACT;
      return KnownChatStep.COMPLETE; // Stay on complete

    default:
      return KnownChatStep.WELCOME;
  }
};
