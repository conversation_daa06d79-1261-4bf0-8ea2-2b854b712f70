import { useState, useEffect, useRef } from "react";
import { X, Calendar, Wrench } from "lucide-react";
import { useReward } from "react-rewards";
import { But<PERSON> } from "@/components/ui/button";
import { MinimalProgressBar } from "./CleanProgressBar";
import { ZipCodeStep } from "./steps/ZipCodeStep";
import { LocationStep } from "./steps/LocationStep";
import { ContactInfoStep } from "./steps/ContactInfoStep";
import { ServiceStep } from "./steps/ServiceStep";
import { ScheduleStep } from "./steps/ScheduleStep";
import { ConfirmationStep } from "./steps/ConfirmationStep";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { usePluginConfig, useCompanyConfig } from "@/config/company";
import { cn } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";

interface BookingData {
  zipCode?: string;
  service?: string;
  description?: string;
  date?: Date;
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  houseNumber?: string;
  street?: string;
  city?: string;
  notes?: string;
}

const STEPS = [
  "Zip Code",
  "Contact Info",
  "Address",
  "Service",
  "Schedule",
  "Confirm",
];

interface PopoverBookingProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: () => void;
}

export function PopoverBooking({
  isOpen,
  onClose,
  onComplete,
}: PopoverBookingProps) {
  const pluginConfig = usePluginConfig();
  const companyConfig = useCompanyConfig();
  const contentRef = useRef<HTMLDivElement>(null);
  const {
    bookingData,
    updateBookingData,
    clearBookingData,
    getDateAsObject,
    setDateFromObject,
  } = useBookingFormStorage();

  const [currentStep, setCurrentStep] = useState(1);
  const [isComplete, setIsComplete] = useState(false);
  const { reward, isAnimating } = useReward("popover-reward-id", "confetti", {
    elementCount: 200,
    startVelocity: 30,
    lifetime: 200,
  });

  // Scroll to top when step changes
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = 0;
    }
  }, [currentStep]);

  // Always start from the first step when popover opens
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
    }
  }, [isOpen]);

  const goToNextStep = (stepData: Partial<BookingData>) => {
    const dataToStore = { ...stepData };
    if (dataToStore.date) {
      setDateFromObject(dataToStore.date);
    }

    updateBookingData({ ...dataToStore, currentStep: currentStep + 1 });
    if (currentStep < STEPS.length) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handleConfirmBooking = () => {
    setIsComplete(true);
    reward(); // Trigger confetti

    // Clear data after a delay
    setTimeout(() => {
      clearBookingData();
      setCurrentStep(1);
      setIsComplete(false);
      onComplete?.();
      onClose();
    }, 3000); // Keep the 3-second delay for closing the popover

    toast({
      title: "Booking Confirmed!",
      description: "We'll be in touch shortly to confirm your appointment.",
    });
  };

  const handleEditStep = (step: string) => {
    switch (step) {
      case "start":
        setCurrentStep(1); // Go back to the first step (Zip Code)
        break;
      case "service":
        setCurrentStep(4); // ServiceStep
        break;
      case "schedule":
        setCurrentStep(5); // ScheduleStep
        break;
      case "contact":
        setCurrentStep(2); // ContactInfoStep
        break;
      case "location":
        setCurrentStep(3); // LocationStep
        break;
      default:
        break;
    }
  };

  const handleStartNewBooking = () => {
    clearBookingData();
    setCurrentStep(1);
    setIsComplete(false);
  };

  const handleLocationStepNext = (
    data: { street: string; city: string; state: string },
    zipCode: string
  ) => {
    goToNextStep({ ...data, zipCode });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-400">
      <span id="popover-reward-id" className="absolute" />
      <div
        className={cn(
          "bg-white dark:bg-gray-800 rounded-lg shadow-2xl border overflow-hidden",
          "w-full max-w-md max-h-[90vh] flex flex-col"
        )}
      >
        {/* Header with Company Branding */}
        <div className="flex items-center justify-between p-3 border-b border-border bg-gradient-to-r from-primary/5 to-primary-light/5">
          <div className="flex items-center gap-3">
            {companyConfig.logo ? (
              <img
                src={companyConfig.logo}
                alt={`${companyConfig.name} Logo`}
                className="w-8 h-8 object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                  e.currentTarget.nextElementSibling?.classList.remove(
                    "hidden"
                  );
                }}
              />
            ) : null}
            <Wrench
              className={cn(
                "w-6 h-6 text-primary",
                companyConfig.logo ? "hidden" : ""
              )}
            />
            <div>
              <h2 className="font-semibold text-foreground text-sm">
                {companyConfig.name.toUpperCase()}
              </h2>
              <p className="text-xs text-muted-foreground">Book Service</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-7 w-7 p-0"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Progress Bar */}
        {!isComplete && (
          <div className="px-3 pt-1">
            <MinimalProgressBar
              currentStep={currentStep}
              totalSteps={STEPS.length}
            />
          </div>
        )}

        {/* Content */}
        <div ref={contentRef} className="flex-1 overflow-y-auto p-3">
          {isComplete ? (
            <div className="text-center py-6">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Calendar className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-bold text-foreground mb-2">
                Booking Confirmed!
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Thank you for choosing our services. We'll be in touch shortly.
              </p>
              <Button
                onClick={handleStartNewBooking}
                variant="outline"
                size="sm"
              >
                Book Another
              </Button>
            </div>
          ) : (
            <>
              {/* Step 1: Zip Code */}
              {currentStep === 1 && (
                <ZipCodeStep
                  onComplete={goToNextStep}
                  initialData={{ zipCode: bookingData.zipCode || "" }}
                />
              )}

              {/* Step 2: Contact Info */}
              {currentStep === 2 && (
                <ContactInfoStep
                  onNext={goToNextStep}
                  initialData={{
                    firstName: bookingData.firstName,
                    lastName: bookingData.lastName,
                    email: bookingData.email,
                    phone: bookingData.phone,
                  }}
                />
              )}

              {/* Step 3: Location */}
              {currentStep === 3 && bookingData.zipCode && (
                <LocationStep
                  onNext={handleLocationStepNext}
                  zipCode={bookingData.zipCode}
                  initialData={{
                    houseNumber: bookingData.houseNumber || "",
                    street: bookingData.street || "",
                    city: bookingData.city || "",
                  }}
                  contactInfo={{
                    firstName: bookingData.firstName || "",
                    lastName: bookingData.lastName || "",
                    email: bookingData.email || "",
                    phone: bookingData.phone || "",
                  }}
                  onEditContact={() => setCurrentStep(2)}
                />
              )}

              {/* Step 4: Service */}
              {currentStep === 4 && (
                <ServiceStep
                  onNext={goToNextStep}
                  initialData={{
                    service: bookingData.service || "",
                    description: bookingData.description || "",
                  }}
                  contactInfo={{
                    firstName: bookingData.firstName || "",
                    lastName: bookingData.lastName || "",
                    email: bookingData.email || "",
                    phone: bookingData.phone || "",
                  }}
                  locationInfo={{
                    zipCode: bookingData.zipCode || "",
                    houseNumber: bookingData.houseNumber || "",
                    street: bookingData.street || "",
                    city: bookingData.city || "",
                  }}
                  onEditContact={() => setCurrentStep(2)}
                  onEditLocation={() => setCurrentStep(3)}
                />
              )}

              {/* Step 5: Schedule */}
              {currentStep === 5 && (
                <ScheduleStep
                  onNext={goToNextStep}
                  initialData={{
                    date: getDateAsObject(),
                    time: bookingData.time || "",
                  }}
                />
              )}

              {/* Step 6: Confirmation */}
              {currentStep === 6 && (
                  <ConfirmationStep
                    data={
                      {
                        ...bookingData,
                        date: getDateAsObject() || new Date(),
                      } as any
                    }
                    onConfirm={handleConfirmBooking}
                    onEdit={handleEditStep} // Simplified for popover
                  />
                )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}


