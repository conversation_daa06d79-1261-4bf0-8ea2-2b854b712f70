import { useState, useEffect, useRef } from "react";
import { useReward } from "react-rewards";
import { X, Send, MessageCircle, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import {
  getKnownChatFlow,
  getNextKnownStep,
  KnownChatStep,
} from "./KnownPersonChatFlow";
import { ChatConfirmationDisplay } from "./ChatConfirmationDisplay";
import { KnownContactInfoStep } from "./steps/KnownContactInfoStep";

interface ChatMessage {
  id: string;
  type: "bot" | "user";
  content: string | React.ReactNode;
  timestamp: Date;
  options?: string[];
  inputType?: "text" | "email" | "tel" | "date" | "time";
  placeholder?: string;
  validation?: (value: string) => string | null;
}

interface KnownPersonChatPluginProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
  className?: string;
}

interface FloatingKnownPersonChatButtonProps {
  onBookingComplete?: (data: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

// Chat Message Component
function ChatMessageComponent({
  message,
  onOptionSelect,
}: {
  message: ChatMessage;
  onOptionSelect: (option: string) => void;
}) {
  if (message.type === "user") {
    return (
      <div className="flex justify-end">
        <div className="max-w-[80%] bg-primary text-primary-foreground rounded-lg px-3 py-2 text-sm">
          {message.content}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-start gap-2">
      <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
        <MessageCircle className="w-3 h-3 text-primary" />
      </div>
      <div className="flex-1">
        {
          typeof message.content === "string"
            ? message.content && (
                <div className="bg-muted rounded-lg px-3 py-2 text-sm text-foreground mb-2">
                  {message.content}
                </div>
              )
            : message.content // Render ReactNode directly
        }

        {/* Option buttons */}
        {message.options && (
          <div className="flex flex-wrap gap-2">
            {message.options.map((option, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => onOptionSelect(option)}
                className="text-xs"
              >
                {option}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export function KnownPersonChatPlugin({
  isOpen,
  onClose,
  onComplete,
  className,
}: KnownPersonChatPluginProps) {
  const companyConfig = useCompanyConfig();
  const { bookingData, updateBookingData, clearBookingData } =
    useBookingFormStorage();

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentStep, setCurrentStep] = useState<KnownChatStep>(
    KnownChatStep.WELCOME
  );
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [selectedService, setSelectedService] = useState("");
  const [selectedSubService, setSelectedSubService] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messageIdCounter = useRef(0);
  const { reward, isAnimating } = useReward("chat-reward-id", "confetti", {
    elementCount: 200,
    startVelocity: 30,
    lifetime: 200,
  });

  // Pre-filled known person data
  const knownPersonData = {
    name: "Thomas Berger",
    email: "<EMAIL>",
    phone: "0987654321",
    address: "Rosenhof 12 8002 Zurich",
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Auto-focus on input when new bot message requires input
  useEffect(() => {
    if (
      inputRef.current &&
      messages.length > 0 &&
      messages[messages.length - 1]?.inputType
    ) {
      inputRef.current.focus();
    }
  }, [messages]);

  // Initialize chat when opened
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      startConversation();
    }
  }, [isOpen, messages.length]);

  const startConversation = () => {
    clearBookingData(); // Clear any previous booking data
    setMessages([]); // Clear previous messages
    setCurrentStep(KnownChatStep.WELCOME); // Start with welcome
    messageIdCounter.current = 0; // Reset counter

    // Welcome message
    const welcomeMessage: ChatMessage = {
      id: `${Date.now()}-${messageIdCounter.current++}`,
      type: "bot",
      content: `Hi! 👋 Welcome back to ${companyConfig.name}! I'm here to help you book your service appointment.`,
      timestamp: new Date(),
    };

    setMessages([welcomeMessage]);

    // Immediately ask for phone number after welcome
    setTimeout(() => {
      addBotMessage(
        "Before we proceed, please confirm your phone number. This helps us ensure we have the correct contact information for your booking.",
        undefined,
        "tel",
        "Enter your phone number...",
        (value: string) => {
          if (!value.trim()) return "Phone number is required.";
          if (!/^\d{10}$/.test(value.replace(/\D/g, "")))
            return "Please enter a valid 10-digit phone number.";
          return null;
        }
      );
      setCurrentStep(KnownChatStep.CONFIRM_CONTACT);
    }, 1500);
  };

  const addBotMessage = (
    content: string | React.ReactNode,
    options?: string[],
    inputType?: ChatMessage["inputType"],
    placeholder?: string,
    validation?: (value: string) => string | null
  ) => {
    setIsTyping(true);

    setTimeout(() => {
      const message: ChatMessage = {
        id: `${Date.now()}-${messageIdCounter.current++}`,
        type: "bot",
        content,
        timestamp: new Date(),
        options,
        inputType,
        placeholder,
        validation,
      };

      setMessages((prev) => [...prev, message]);
      setIsTyping(false);
    }, 1000); // Simulate typing delay
  };

  const addUserMessage = (content: string) => {
    const message: ChatMessage = {
      id: `${Date.now()}-${messageIdCounter.current++}`,
      type: "user",
      content,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, message]);
    setInputValue("");
  };

  // Handle option selection
  const handleOptionSelect = (option: string) => {
    addUserMessage(option);
    processUserResponse(option);
  };

  // Handle text input submission
  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) {
      setInputValue(""); // Clear input even if empty
      return;
    }

    // Validate if validation function exists
    const currentFlow = getKnownChatFlow(currentStep, {
      ...bookingData,
      selectedService,
      selectedSubService,
    });
    if (currentFlow.validation) {
      const error = currentFlow.validation(inputValue);
      if (error) {
        toast({
          title: "Invalid Input",
          description: error,
          variant: "destructive",
        });
        setInputValue(""); // Clear input on validation error
        return;
      }
    }

    addUserMessage(inputValue);
    processUserResponse(inputValue);
  };

  // Process user responses and advance conversation
  const processUserResponse = (response: string | any) => {
    // Update booking data based on current step
    const updatedData = { ...bookingData };

    switch (currentStep) {
      case KnownChatStep.WELCOME:
        // Just proceed to next step
        break;
      case KnownChatStep.CONFIRM_CONTACT:
        // Handle phone number input
        updatedData.phone = response;

        // Check if this is a known user phone number
        if (response === "0987654321") {
          // Known user - show their details and ask for confirmation
          updatedData.firstName = "Thomas";
          updatedData.lastName = "Berger";
          updatedData.email = "<EMAIL>";
          updatedData.address = "Rosenhof 12, 8002 Zurich";
          updatedData.zipCode = "8002";

          addBotMessage(
            `Great! I found your details:\n\n👤 Thomas Berger\n📧 <EMAIL>\n📍 Rosenhof 12, 8002 Zurich\n\nIs this information correct?`,
            ["Yes, that's correct", "No, I need to update it"]
          );
        } else {
          // Unknown user - proceed to service selection
          addBotMessage(
            "Thank you! Now let's proceed with your service request."
          );
          setCurrentStep(KnownChatStep.SERVICE_TYPE);
          const flow = getKnownChatFlow(
            KnownChatStep.SERVICE_TYPE,
            updatedData
          );
          addBotMessage(flow.content, flow.options);
          return;
        }
        break;
      case KnownChatStep.SERVICE_TYPE:
        setSelectedService(response);
        updatedData.service = response;
        addBotMessage(`You selected ${updatedData.service}.`);
        break;
      case KnownChatStep.SERVICE_CATEGORY:
        setSelectedSubService(response);
        updatedData.service = `${selectedService} - ${response}`;
        addBotMessage(
          `Understood. You need ${response} for ${selectedService}.`
        );
        break;
      case KnownChatStep.SERVICE_SPECIFIC:
        updatedData.service = `${selectedService} - ${selectedSubService} - ${response}`;
        addBotMessage(`Okay, specifically ${response}.`);
        break;
      case KnownChatStep.SERVICE_DESCRIPTION:
        if (response === "Skip") {
          updatedData.description = ""; // Set description to empty if skipped
          addBotMessage(`Okay, skipping the description.`);
        } else {
          updatedData.description = response.trim();
          addBotMessage(`I've noted your description.`);
        }
        break;
      case KnownChatStep.SCHEDULE_DATE:
        const parts = response.split(", ");
        const dateString = `${parts[0]}, ${parts[1]} ${parts[2]}, ${parts[3]}`;
        const timeRange = parts[4];

        updatedData.date = dateString;
        updatedData.time = timeRange;
        addBotMessage(
          `You'd like to schedule for ${dateString} at ${timeRange}.`
        );
        break;
      case KnownChatStep.SCHEDULE_TIME:
        updatedData.time = response;
        addBotMessage(`And the best time is ${updatedData.time}.`);
        break;
      case KnownChatStep.CONFIRMATION:
        if (response.includes("book")) {
          if (!termsAccepted) {
            addBotMessage("Please accept the Terms and Conditions to proceed.");
            return;
          }
          handleBookingComplete(updatedData);
          return;
        } else {
          setMessages([]);
          setCurrentStep(KnownChatStep.WELCOME);
          startConversation();
          return;
        }
      case KnownChatStep.COMPLETE:
        if (response.includes("another")) {
          clearBookingData();
          setMessages([]);
          setCurrentStep(KnownChatStep.WELCOME);
          startConversation();
          return;
        } else if (response.includes("That's all, thanks!")) {
          onComplete?.(bookingData);
          return;
        } else {
          onComplete?.(bookingData);
          return;
        }
    }

    updateBookingData(updatedData);

    // Handle details confirmation response
    if (
      currentStep === KnownChatStep.CONFIRM_CONTACT &&
      (response.includes("Yes") || response.includes("No"))
    ) {
      if (response.includes("Yes")) {
        // User confirmed details, proceed to service selection
        setCurrentStep(KnownChatStep.SERVICE_TYPE);
        const flow = getKnownChatFlow(KnownChatStep.SERVICE_TYPE, updatedData);
        addBotMessage(flow.content, flow.options);
        return;
      } else {
        // User wants to update details, restart the process
        addBotMessage(
          "No problem! Let's get your updated information. Please enter your phone number again."
        );
        setCurrentStep(KnownChatStep.CONFIRM_CONTACT);
        return;
      }
    }

    const nextStep = getNextKnownStep(currentStep, response, {
      selectedService,
      selectedSubService,
      ...updatedData,
      response: response,
    });

    setCurrentStep(nextStep);

    const flow = getKnownChatFlow(nextStep, {
      companyName: companyConfig.name,
      selectedService:
        currentStep === KnownChatStep.SERVICE_TYPE ? response : selectedService,
      selectedSubService,
      ...updatedData,
    });

    // Special handling for Confirmation Step and KnownContactInfoStep
    if (nextStep === KnownChatStep.CONFIRMATION) {
      flow.content = (
        <ChatConfirmationDisplay
          data={updatedData}
          onTermsAcceptedChange={setTermsAccepted}
        />
      );
    } else if (nextStep === KnownChatStep.CONFIRM_CONTACT) {
      flow.content = (
        <KnownContactInfoStep
          initialData={knownPersonData}
          onNext={(contactData) => {
            updateBookingData({
              ...updatedData,
              name: contactData.name,
              email: contactData.email,
              phone: contactData.phone,
              address: contactData.address,
            });
            // Manually advance the chat flow after contact confirmation
            addBotMessage("Thank you for confirming your details!");
            processUserResponse("contact_confirmed"); // Use a dummy response to trigger next step
          }}
        />
      );
    }

    addBotMessage(
      flow.content,
      flow.options,
      flow.inputType,
      flow.placeholder,
      flow.validation
    );
  };

  const handleBookingComplete = (data: any) => {
    toast({
      title: "Booking Confirmed!",
      description:
        "Your appointment has been successfully scheduled. You'll receive a confirmation email shortly.",
    });

    reward();

    const flow = getKnownChatFlow(KnownChatStep.COMPLETE, {
      companyName: companyConfig.name,
      ...data,
    });

    addBotMessage(flow.content, flow.options);
    setCurrentStep(KnownChatStep.COMPLETE);
  };

  return (
    <div
      className={cn(
        "fixed z-50 transition-all duration-300 ease-in-out",
        isOpen
          ? "bottom-4 right-1/2 translate-x-1/2 opacity-100 translate-y-0"
          : "bottom-4 right-1/2 translate-x-1/2 opacity-0 translate-y-4 pointer-events-none",
        className
      )}
    >
      <Card
        className={cn(
          "w-full max-w-lg h-[600px] flex flex-col bg-white dark:bg-gray-800 shadow-2xl",
          className
        )}
      >
        <span
          id="chat-reward-id"
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
        />
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-white text-gray-900 rounded-t-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <img
                src={companyConfig.logo}
                alt={`${companyConfig.name} Logo`}
                className="w-8 h-8 object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                  e.currentTarget.nextElementSibling?.classList.remove(
                    "hidden"
                  );
                }}
              />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{companyConfig.name}</h3>
              <p className="text-xs text-gray-500">Booking Assistant</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={startConversation}
              className="text-gray-400 hover:text-gray-600 h-8 w-8"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 h-8 w-8"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <ChatMessageComponent
              key={message.id}
              message={message}
              onOptionSelect={handleOptionSelect}
            />
          ))}

          {/* Typing indicator */}
          {isTyping && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                <MessageCircle className="w-3 h-3 text-primary" />
              </div>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" />
                <div
                  className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                />
                <div
                  className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                />
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Always visible input area */}
        {currentStep !== KnownChatStep.CONFIRM_CONTACT && (
          <form onSubmit={handleInputSubmit} className="p-4 border-t">
            <div className="flex gap-2">
              <Input
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 min-w-0"
                autoFocus
              />
              <button
                type="submit"
                className="w-10 h-10 rounded-full p-0 flex-grow-0 flex-shrink-0 inline-flex items-center justify-center bg-primary text-primary-foreground hover:bg-primary/90 transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
                disabled={!inputValue.trim()}
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </form>
        )}
      </Card>
    </div>
  );
}

// Floating Button Component
export function FloatingKnownPersonChatButton({
  onBookingComplete,
  onBookingStart,
  className,
}: FloatingKnownPersonChatButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className={cn("fixed bottom-6 left-6 z-40", className)}>
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-16 h-16 rounded-full bg-gray-900 hover:bg-gray-800 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 group p-0"
        >
          <div className="flex flex-col items-center">
            <MessageCircle className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span className="text-xs font-medium">Known Chat</span>
          </div>
        </Button>
      </div>

      <KnownPersonChatPlugin
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
          setIsOpen(false);
        }}
      />
    </>
  );
}
