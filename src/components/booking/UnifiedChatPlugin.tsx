import { useState, useEffect, useRef } from "react";
import { useReward } from "react-rewards";
import { X, Send, MessageCircle, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import { getChatFlow, getNextStep, ChatStep } from "./ChatFlow";
import { findUserByPhone } from "@/data/knownUsers";

interface ChatMessage {
  id: string;
  type: "user" | "bot";
  content: string;
  timestamp: Date;
  options?: string[];
  inputType?: "text" | "email" | "tel" | "date" | "time";
  placeholder?: string;
  validation?: (value: string) => string | null;
  showTermsCheckbox?: boolean;
}

// Chat Message Component
function ChatMessageComponent({
  message,
  onOptionSelect,
  onInputSubmit,
  onTermsAcceptedChange,
  termsAccepted,
}: {
  message: ChatMessage;
  onOptionSelect: (option: string) => void;
  onInputSubmit: (value: string) => void;
  onTermsAcceptedChange?: (accepted: boolean) => void;
  termsAccepted?: boolean;
}) {
  const [inputValue, setInputValue] = useState("");

  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    if (message.validation) {
      const error = message.validation(inputValue);
      if (error) {
        toast({
          title: "Invalid Input",
          description: error,
          variant: "destructive",
        });
        return;
      }
    }

    onInputSubmit(inputValue);
    setInputValue("");
  };

  return (
    <div className={`flex ${message.type === "user" ? "justify-end" : "justify-start"} mb-4`}>
      <div
        className={`max-w-[80%] p-3 rounded-lg ${
          message.type === "user"
            ? "bg-purple-600 text-white"
            : "bg-gray-100 text-gray-900"
        }`}
      >
        <p className="text-sm whitespace-pre-wrap">{message.content}</p>

        {/* Option buttons */}
        {message.options && (
          <div className="flex flex-wrap gap-2 mt-3">
            {message.options.map((option, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => onOptionSelect(option)}
                className="text-xs bg-white hover:bg-gray-50 text-gray-900 border-gray-300"
              >
                {option}
              </Button>
            ))}
          </div>
        )}

        {/* Input field for text responses */}
        {message.inputType && (
          <form onSubmit={handleInputSubmit} className="mt-2">
            <div className="flex gap-2">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={message.placeholder || "Type your response..."}
                type={message.inputType}
                className="flex-1 text-sm"
              />
              <Button type="submit" size="sm" disabled={!inputValue.trim()}>
                <Send className="w-3 h-3" />
              </Button>
            </div>
            {message.inputType === "tel" && message.showTermsCheckbox && (
              <div className="flex items-center space-x-2 mt-2">
                <Checkbox
                  id="terms"
                  checked={termsAccepted}
                  onCheckedChange={onTermsAcceptedChange}
                />
                <label
                  htmlFor="terms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I accept the{" "}
                  <a href="#" className="underline text-purple-600">
                    terms and conditions
                  </a>
                </label>
              </div>
            )}
          </form>
        )}
      </div>
    </div>
  );
}

interface UnifiedChatPluginProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
  className?: string;
}

interface FloatingUnifiedChatButtonProps {
  onBookingComplete?: (data: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

export function UnifiedChatPlugin({
  isOpen,
  onClose,
  onComplete,
  className,
}: UnifiedChatPluginProps) {
  const companyConfig = useCompanyConfig();
  const { bookingData, updateBookingData, clearBookingData } = useBookingFormStorage();
  const { reward } = useReward("unified-chat-reward-id", "confetti", {
    elementCount: 200,
    startVelocity: 30,
    lifetime: 200,
  });

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentStep, setCurrentStep] = useState<ChatStep>(ChatStep.WELCOME);
  const [isTyping, setIsTyping] = useState(false);
  const [selectedService, setSelectedService] = useState("");
  const [selectedSubService, setSelectedSubService] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [foundUser, setFoundUser] = useState<any>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  let messageIdCounter = useRef(0);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addBotMessage = (
    content: string,
    options?: string[],
    inputType?: "text" | "email" | "tel" | "date" | "time",
    placeholder?: string,
    validation?: (value: string) => string | null
  ) => {
    setIsTyping(true);
    setTimeout(() => {
      const message: ChatMessage = {
        id: `${Date.now()}-${messageIdCounter.current++}`,
        type: "bot",
        content,
        timestamp: new Date(),
        options,
        inputType,
        placeholder,
        validation,
        showTermsCheckbox: inputType === "tel",
      };
      setMessages((prev) => [...prev, message]);
      setIsTyping(false);
    }, 1000);
  };

  const addUserMessage = (content: string) => {
    const message: ChatMessage = {
      id: `${Date.now()}-${messageIdCounter.current++}`,
      type: "user",
      content,
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, message]);
  };

  const startConversation = () => {
    clearBookingData();
    setMessages([]);
    setCurrentStep(ChatStep.WELCOME);
    setFoundUser(null);
    setTermsAccepted(false);
    messageIdCounter.current = 0;

    // Welcome message
    const welcomeMessage: ChatMessage = {
      id: `${Date.now()}-${messageIdCounter.current++}`,
      type: "bot",
      content: `Hi! 👋 Welcome to ${companyConfig.name}! I'm here to help you book your service appointment.`,
      timestamp: new Date(),
    };

    setMessages([welcomeMessage]);

    // Ask for phone number
    setTimeout(() => {
      addBotMessage(
        "Let's start by getting your phone number. This helps us check if you're an existing customer and provide you with the best service.",
        undefined,
        "tel",
        "Enter your phone number...",
        (value: string) => {
          if (!value.trim()) return "Phone number is required.";
          if (!/^\d{10}$/.test(value.replace(/\D/g, ""))) return "Please enter a valid 10-digit phone number.";
          return null;
        }
      );
      setCurrentStep(ChatStep.PHONE);
    }, 1500);
  };

  const processUserResponse = (response: string) => {
    const updatedData = { ...bookingData };

    switch (currentStep) {
      case ChatStep.PHONE:
        if (!termsAccepted) {
          toast({
            title: "Terms Required",
            description: "Please accept the terms and conditions to proceed.",
            variant: "destructive",
          });
          return;
        }
        
        updatedData.phone = response;
        
        // Check if this is a known user
        const user = findUserByPhone(response);
        if (user) {
          setFoundUser(user);
          updatedData.firstName = user.name?.split(' ')[0] || user.firstName;
          updatedData.lastName = user.name?.split(' ')[1] || user.lastName || '';
          updatedData.email = user.email;
          
          addBotMessage(
            `Great! I found your details:\n\n👤 ${user.name}\n📧 ${user.email}\n📍 ${user.address}\n\nIs this information correct?`,
            ["Yes, that's correct", "No, I need to update it"]
          );
          setCurrentStep(ChatStep.CONFIRM_CONTACT);
        } else {
          addBotMessage("Thank you! What's your full name?", undefined, "text", "Enter your full name");
          setCurrentStep(ChatStep.NAME);
        }
        break;

      case ChatStep.CONFIRM_CONTACT:
        if (response.includes("Yes")) {
          // User confirmed details, proceed to service selection
          setCurrentStep(ChatStep.SERVICE_TYPE);
          const flow = getChatFlow(ChatStep.SERVICE_TYPE, updatedData);
          addBotMessage(flow.content, flow.options);
          break;
        } else {
          // User wants to update details
          addBotMessage("No problem! What's your full name?", undefined, "text", "Enter your full name");
          setCurrentStep(ChatStep.NAME);
          break;
        }

      case ChatStep.NAME:
        updatedData.firstName = response.split(' ')[0];
        updatedData.lastName = response.split(' ').slice(1).join(' ');
        addBotMessage("Great! What's your email address?", undefined, "email", "Enter your email address");
        setCurrentStep(ChatStep.EMAIL);
        break;

      case ChatStep.EMAIL:
        updatedData.email = response;
        if (!foundUser) {
          addBotMessage("Perfect! What's your address?", undefined, "text", "Enter your complete address");
          setCurrentStep(ChatStep.ADDRESS);
        } else {
          setCurrentStep(ChatStep.SERVICE_TYPE);
          const flow = getChatFlow(ChatStep.SERVICE_TYPE, updatedData);
          addBotMessage(flow.content, flow.options);
        }
        break;

      case ChatStep.ADDRESS:
        updatedData.street = response;
        setCurrentStep(ChatStep.SERVICE_TYPE);
        const flow = getChatFlow(ChatStep.SERVICE_TYPE, updatedData);
        addBotMessage(flow.content, flow.options);
        break;

      default:
        // Handle other steps using existing flow
        const nextStep = getNextStep(currentStep, response, {
          selectedService,
          selectedSubService,
          ...updatedData,
          response: response,
        });
        setCurrentStep(nextStep);
        break;
    }

    updateBookingData(updatedData);

    // Handle service selection
    if (currentStep === ChatStep.SERVICE_TYPE) {
      setSelectedService(response);
    } else if (currentStep === ChatStep.SUB_SERVICE) {
      setSelectedSubService(response);
    }

    // Handle completion
    if (currentStep === ChatStep.CONFIRMATION && response.toLowerCase().includes("confirm")) {
      toast({
        title: "Booking Confirmed!",
        description: "Your appointment has been successfully scheduled.",
      });
      reward();
      setTimeout(() => {
        onComplete?.(updatedData);
        onClose();
      }, 2000);
    }
  };

  useEffect(() => {
    if (isOpen) {
      startConversation();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-md h-[600px] flex flex-col bg-white shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-t-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <MessageCircle className="w-4 h-4" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{companyConfig.name}</h3>
              <p className="text-xs opacity-90">Booking Assistant</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={startConversation}
              className="text-white hover:bg-white/20 h-8 w-8"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/20 h-8 w-8"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <ChatMessageComponent
              key={message.id}
              message={message}
              onOptionSelect={(option) => {
                addUserMessage(option);
                processUserResponse(option);
              }}
              onInputSubmit={(value) => {
                addUserMessage(value);
                processUserResponse(value);
              }}
              onTermsAcceptedChange={setTermsAccepted}
              termsAccepted={termsAccepted}
            />
          ))}

          {isTyping && (
            <div className="flex justify-start">
              <div className="bg-gray-100 p-3 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Reward Animation */}
        <div id="unified-chat-reward-id" className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none z-50" />
      </Card>
    </div>
  );
}

// Floating Button Component
export function FloatingUnifiedChatButton({
  onBookingComplete,
  onBookingStart,
  className,
}: FloatingUnifiedChatButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className={cn("fixed bottom-6 left-6 z-40", className)}>
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 group p-0"
        >
          <div className="flex flex-col items-center">
            <MessageCircle className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span className="text-xs font-medium">Chat</span>
          </div>
        </Button>
      </div>

      <UnifiedChatPlugin
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
          setIsOpen(false);
        }}
      />
    </>
  );
}
