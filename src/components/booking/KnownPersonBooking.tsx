import { useState, useEffect } from "react";
import { X, Calendar, Wrench } from "lucide-react";
import { useReward } from "react-rewards";
import { Button } from "@/components/ui/button";
import { MinimalProgressBar } from "./CleanProgressBar";
import { ServiceStep } from "./steps/ServiceStep";
import { ScheduleStep } from "./steps/ScheduleStep";
import { ConfirmationStep } from "./steps/ConfirmationStep";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { usePluginConfig, useCompanyConfig } from "@/config/company";
import { cn } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";

interface BookingData {
  service?: string;
  description?: string;
  date?: Date;
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  houseNumber?: string;
  street?: string;
  city?: string;
  zipCode?: string;
  notes?: string;
}

const STEPS = ["Service", "Schedule", "Confirm"];

interface KnownPersonBookingProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: () => void;
  initialData?: any;
}

export function KnownPersonBooking({
  isOpen,
  onClose,
  onComplete,
  initialData,
}: KnownPersonBookingProps) {
  const companyConfig = useCompanyConfig();
  const pluginConfig = usePluginConfig();
  const {
    bookingData,
    updateBookingData,
    clearBookingData,
    getDateAsObject,
    setDateFromObject,
  } = useBookingFormStorage();
  const { reward, isAnimating } = useReward(
    "known-booking-reward-id",
    "confetti",
    {
      elementCount: 200,
      startVelocity: 30,
      lifetime: 200,
    }
  );

  const [currentStep, setCurrentStep] = useState(1);
  const [isComplete, setIsComplete] = useState(false);

  // Initialize with known person data
  useEffect(() => {
    if (isOpen && initialData) {
      updateBookingData({
        firstName: initialData.firstName || initialData.name?.split(" ")[0],
        lastName: initialData.lastName || initialData.name?.split(" ")[1] || "",
        email: initialData.email,
        phone: initialData.phone,
        street: initialData.address?.split(",")[0] || initialData.street,
        city: initialData.city || "Zurich",
        zipCode: initialData.zipCode,
      });
    }
  }, [isOpen, initialData, updateBookingData]);

  const goToNextStep = (stepData: Partial<BookingData>) => {
    const dataToStore = { ...stepData };
    if (dataToStore.date) {
      setDateFromObject(dataToStore.date);
      delete dataToStore.date;
    }
    updateBookingData(dataToStore);
    if (currentStep < STEPS.length) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const goToPreviousStep = () => {
    setCurrentStep((prev) => prev - 1);
  };

  const handleEditStep = (step: string) => {
    switch (step) {
      case "service":
        setCurrentStep(1);
        break;
      case "schedule":
        setCurrentStep(2);
        break;
      default:
        setCurrentStep(1);
    }
  };

  const handleConfirmBooking = () => {
    const finalData = {
      ...bookingData,
      date: getDateAsObject(),
    };

    toast({
      title: "Booking Confirmed!",
      description: "Your appointment has been successfully scheduled.",
    });

    reward();
    setIsComplete(true);

    setTimeout(() => {
      onComplete?.();
      onClose();
      clearBookingData();
      setCurrentStep(1);
      setIsComplete(false);
    }, 3000);
  };

  const renderCurrentStep = () => {
    const currentData = {
      ...bookingData,
      date: getDateAsObject(),
    };

    switch (currentStep) {
      case 1:
        return (
          <ServiceStep
            data={currentData}
            onNext={goToNextStep}
            onBack={() => onClose()}
          />
        );
      case 2:
        return (
          <ScheduleStep
            data={currentData}
            onNext={goToNextStep}
            onBack={goToPreviousStep}
          />
        );
      case 3:
        return (
          <ConfirmationStep
            data={currentData}
            onConfirm={handleConfirmBooking}
            onEdit={handleEditStep}
          />
        );
      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-purple-600 to-purple-700 text-white">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Wrench className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold">{companyConfig.name}</h1>
              <p className="text-sm opacity-90">Book Your Service</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-white hover:bg-white/20"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Progress */}
        <div className="px-6 py-4 bg-purple-50 border-b">
          <MinimalProgressBar
            currentStep={currentStep}
            totalSteps={STEPS.length}
            steps={STEPS}
          />
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {renderCurrentStep()}
        </div>

        {/* Reward Animation */}
        <div
          id="known-booking-reward-id"
          className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none z-50"
        />
      </div>
    </div>
  );
}
