import { useState, useEffect } from "react";
import { User, X, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import { findUserByPhone } from "@/data/knownUsers";
import { KnownPersonBooking } from "./KnownPersonBooking";

// Known user details - same as working chat
const KNOWN_USER = {
  firstName: "Thomas",
  lastName: "<PERSON>",
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "0987654321",
  address: "Rosenhof 12, 8002 Zurich",
  zipCode: "8002",
};

interface KnownPersonFormPluginProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
  className?: string;
}

interface FloatingKnownPersonFormButtonProps {
  onBookingComplete?: (data: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

enum FormStep {
  PHONE_INPUT = "phoneInput",
  VERIFY_DETAILS = "verifyDetails",
  BOOKING_FORM = "bookingForm",
}

export function KnownPersonFormPlugin({
  isOpen,
  onClose,
  onComplete,
  className,
}: KnownPersonFormPluginProps) {
  const companyConfig = useCompanyConfig();
  const { bookingData, updateBookingData, clearBookingData } =
    useBookingFormStorage();

  const [currentStep, setCurrentStep] = useState<FormStep>(
    FormStep.PHONE_INPUT
  );
  const [phoneNumber, setPhoneNumber] = useState("");
  const [foundUser, setFoundUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(FormStep.PHONE_INPUT);
      setPhoneNumber("");
      setFoundUser(null);
      setTermsAccepted(false);
    }
  }, [isOpen]);

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!phoneNumber.trim()) return;

    if (!termsAccepted) {
      toast({
        title: "Terms Required",
        description: "Please accept the terms and conditions to proceed.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Check if it's Thomas's number (same logic as working chat)
    if (phoneNumber === KNOWN_USER.phone || phoneNumber === "0987654321") {
      setFoundUser(KNOWN_USER);
      updateBookingData(KNOWN_USER);
      setCurrentStep(FormStep.VERIFY_DETAILS);
      toast({
        title: "User Found!",
        description: `Welcome back, ${KNOWN_USER.firstName}!`,
      });
    } else {
      toast({
        title: "User Not Found",
        description:
          "This phone number is not in our system. Please use the Unknown Person form.",
        variant: "destructive",
      });
    }

    setIsLoading(false);
  };

  const handleDetailsConfirm = () => {
    setCurrentStep(FormStep.BOOKING_FORM);
  };

  const handleDetailsUpdate = () => {
    toast({
      title: "Update Required",
      description: "Please contact us to update your details, then try again.",
      variant: "destructive",
    });
  };

  const handleBookingComplete = (data: any) => {
    toast({
      title: "Booking Confirmed!",
      description: "Your appointment has been successfully scheduled.",
    });
    onComplete?.(data);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
      <Card
        className={cn(
          "w-full max-w-4xl bg-white dark:bg-gray-800 shadow-2xl max-h-[90vh] overflow-hidden",
          currentStep === FormStep.BOOKING_FORM ? "max-w-4xl" : "max-w-md",
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-white text-gray-900 rounded-t-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-gray-600" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{companyConfig.name}</h3>
              <p className="text-xs text-gray-500">Known Customer Form</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 h-8 w-8"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {currentStep === FormStep.PHONE_INPUT && (
            <div className="space-y-4">
              <div className="text-center">
                <h2 className="text-xl font-semibold mb-2">Welcome Back!</h2>
                <p className="text-muted-foreground text-sm">
                  Enter your phone number to retrieve your details
                </p>
              </div>

              <form onSubmit={handlePhoneSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder="Enter your phone number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                    required
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    checked={termsAccepted}
                    onCheckedChange={setTermsAccepted}
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    I accept the{" "}
                    <a href="#" className="underline text-gray-600">
                      terms and conditions
                    </a>
                  </label>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-gray-900 hover:bg-gray-800 text-white"
                  disabled={isLoading || !termsAccepted}
                >
                  {isLoading ? "Searching..." : "Find My Details"}
                </Button>
              </form>
            </div>
          )}

          {currentStep === FormStep.VERIFY_DETAILS && foundUser && (
            <div className="space-y-4">
              <div className="text-center">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-3" />
                <h2 className="text-xl font-semibold mb-2">Details Found!</h2>
                <p className="text-muted-foreground text-sm">
                  Please verify your information below
                </p>
              </div>

              <div className="bg-muted/50 rounded-lg p-4 space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Name:</span>
                    <p>{foundUser.name}</p>
                  </div>
                  <div>
                    <span className="font-medium">Phone:</span>
                    <p>{foundUser.phone}</p>
                  </div>
                  <div>
                    <span className="font-medium">Email:</span>
                    <p>{foundUser.email}</p>
                  </div>
                  <div>
                    <span className="font-medium">Zip Code:</span>
                    <p>{foundUser.zipCode}</p>
                  </div>
                </div>
                <div className="text-sm">
                  <span className="font-medium">Address:</span>
                  <p>{foundUser.address}</p>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={handleDetailsConfirm}
                  className="flex-1 bg-purple-600 hover:bg-purple-700"
                >
                  Yes, that's correct
                </Button>
                <Button
                  onClick={handleDetailsUpdate}
                  variant="outline"
                  className="flex-1"
                >
                  Need to update
                </Button>
              </div>
            </div>
          )}

          {currentStep === FormStep.BOOKING_FORM && (
            <KnownPersonBooking
              isOpen={true}
              onClose={() => setCurrentStep(FormStep.VERIFY_DETAILS)}
              onComplete={() => handleBookingComplete(foundUser)}
              initialData={foundUser}
            />
          )}
        </div>
      </Card>
    </div>
  );
}

// Floating Button Component
export function FloatingKnownPersonFormButton({
  onBookingComplete,
  onBookingStart,
  className,
}: FloatingKnownPersonFormButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className={cn("fixed bottom-6 left-6 z-40", className)}>
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-16 h-16 rounded-full bg-gray-900 hover:bg-gray-800 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 group p-0"
        >
          <div className="flex flex-col items-center">
            <CheckCircle className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span className="text-xs font-medium">Known Form</span>
          </div>
        </Button>
      </div>

      <KnownPersonFormPlugin
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
          setIsOpen(false);
        }}
      />
    </>
  );
}
