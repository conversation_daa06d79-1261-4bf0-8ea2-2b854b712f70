import React from "react";

import { ConfirmationStep } from "./steps/ConfirmationStep";
import { ZipCodeStep } from "./steps/ZipCodeStep";

// Chat conversation steps - natural flow, one field at a time
export enum ChatStep {
  WELCOME = "welcome",
  ZIP_CODE = "zipCode",
  NAME = "name",
  EMAIL = "email",
  PHONE = "phone",
  ADDRESS = "address",
  CITY = "city",
  SERVICE_TYPE = "serviceType",
  SERVICE_CATEGORY = "serviceCategory",
  SERVICE_SPECIFIC = "serviceSpecific",
  SERVICE_DESCRIPTION = "serviceDescription",
  SCHEDULE_DATE = "scheduleDate",
  SCHEDULE_TIME = "scheduleTime",
  CONFIRMATION = "confirmation",
  COMPLETE = "complete",
  VERIFY_DETAILS = "verifyDetails",
}

export interface ChatFlowReturn {
  content?: string | React.ReactNode;
  options?: string[];
  inputType?: "text" | "email" | "tel" | "date" | "time";
  placeholder?: string;
  validation?: (value: string) => string | null;
}

import { services } from "@/config/company";
import { format } from "date-fns";

// Validation functions
export const validators = {
  zipCode: (value: string) => {
    if (!value.trim()) return "Please enter your zip code";
    if (!/^\d{5}(-\d{4})?$/.test(value.trim()))
      return "Please enter a valid zip code (e.g., 12345)";
    return null;
  },

  name: (value: string) => {
    if (!value.trim()) return "Please enter your full name";
    if (value.trim().length < 3) return "Name must be at least 3 characters";
    return null;
  },

  email: (value: string) => {
    if (!value.trim()) return "Please enter your email address";
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value.trim()))
      return "Please enter a valid email address";
    return null;
  },

  phone: (value: string) => {
    if (!value.trim()) return "Please enter your phone number";
    const cleaned = value.replace(/\D/g, "");
    if (cleaned.length < 10) return "Please enter a valid phone number";
    return null;
  },

  address: (value: string) => {
    if (!value.trim()) return "Please enter your address";
    if (value.trim().length < 10) return "Please enter a complete address";
    return null;
  },

  fullAddress: (value: string) => {
    if (!value.trim()) return "Please enter your full address.";
    return null;
  },
};

// Helper to generate available time slots for chat
const generateChatAvailableSlots = () => {
  const slots = [];
  const timeSlots = [
    "08:00 - 10:00",
    "10:00 - 12:00",
    "12:00 - 14:00",
    "14:00 - 16:00",
    "16:00 - 18:00",
  ];
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let i = 0; i < 5; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + 1 + i); // Start from tomorrow
    const timeRange = timeSlots[i % timeSlots.length]; // Cycle through time slots
    slots.push(`${format(date, "EEE, MMM do, yyyy")}, ${timeRange}`); // MODIFIED LINE
  }
  return slots;
};

// Chat flow messages and logic
export const getChatFlow = (step: ChatStep, data?: any): ChatFlowReturn => {
  switch (step) {
    case ChatStep.WELCOME:
      return {
        content: `Hi there! 👋 Welcome to ${
          data?.companyName || "our service"
        }! I'm here to help you book your service appointment. This will only take a few minutes, and I'll guide you through each step.`,
        options: undefined,
      };

    case ChatStep.VERIFY_DETAILS:
      return {
        content: (
          <div>
            <p>I have the following details on file for you:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>
                <strong>Name:</strong> {data?.name}
              </li>
              <li>
                <strong>Email:</strong> {data?.email}
              </li>
              <li>
                <strong>Phone:</strong> {data?.phone}
              </li>
              <li>
                <strong>Address:</strong> {data?.address}
              </li>
              <li>
                <strong>Zip Code:</strong> {data?.zipCode}
              </li>
            </ul>
            <p className="mt-3">Is this information correct?</p>
          </div>
        ),
        options: ["Yes, that's correct!", "No, I need to update"],
      };

    case ChatStep.ZIP_CODE:
      return {
        content:
          "Great! Let's start with your location. What's your zip code? This helps us confirm we service your area.",
      };

    case ChatStep.NAME:
      return {
        content: "Perfect! We service your area. What's your full name?",
      };

    case ChatStep.EMAIL:
      return {
        content: `Thanks ${data?.name}! What's your email address? We'll send you booking confirmations here.`,
      };

    case ChatStep.PHONE:
      return {
        content:
          "And what's the best phone number to reach you? Our technician will call when they're on their way.",
      };

    case ChatStep.ADDRESS:
      return {
        content:
          "Now let's get your service address. What's your complete address? (Include street, house number, and city)",
      };

    case ChatStep.SERVICE_TYPE:
      return {
        content:
          "Now for the fun part! What type of service do you need? I can help with all of these:",
        options: Object.keys(services),
      };

    case ChatStep.SERVICE_CATEGORY:
      const currentService =
        data?.service?.split(" - ")[0] || data?.selectedService; // Get the main service
      const serviceTypes = services[currentService as keyof typeof services];
      return {
        content: `Great choice! For ${currentService}, what type of work do you need?`,
        options: Object.keys(serviceTypes || {}),
      };

    case ChatStep.SERVICE_SPECIFIC:
      const mainService =
        data?.service?.split(" - ")[0] || data?.selectedService;
      const subService =
        data?.service?.split(" - ")[1] || data?.selectedSubService;

      const specificServices =
        services[mainService as keyof typeof services]?.[
          subService as keyof (typeof services)[keyof typeof services]
        ];
      if (Array.isArray(specificServices) && specificServices.length > 0) {
        return {
          content: `Perfect! What specifically needs to be ${subService?.toLowerCase()}?`,
          options: specificServices,
        };
      } else {
        // Skip to description if no specific options
        return getChatFlow(ChatStep.SERVICE_DESCRIPTION, data);
      }

    case ChatStep.SERVICE_DESCRIPTION:
      return {
        content:
          "Got it! Is there anything specific about your issue you'd like me to know? This helps our technician come prepared with the right tools and parts. (Optional - you can skip this)",
        options: ["Skip"], // Added Skip option
      };

    case ChatStep.SCHEDULE_DATE:
      const availableSlots = generateChatAvailableSlots();
      return {
        content:
          "Almost done! When would you like to schedule your appointment? Here are some first available options:",
        options: availableSlots,
        validation: (value: string) => {
          if (!value.trim()) return "Please select an appointment slot.";
          if (!availableSlots.includes(value))
            return "Please select a valid appointment slot from the options.";
          return null;
        },
      };

    case ChatStep.SCHEDULE_TIME:
      return {
        content: "What time works best for you?",
        options: [
          "8:00 AM - 10:00 AM",
          "10:00 AM - 12:00 PM",
          "12:00 PM - 2:00 PM",
          "2:00 PM - 4:00 PM",
          "4:00 PM - 6:00 PM",
        ],
      };

    case ChatStep.CONFIRMATION:
      return {
        content: null, // This will be replaced by a React component
        options: ["Yes, book it!", "Let me make changes"],
      };

    case ChatStep.COMPLETE:
      return {
        content: `🎉 Awesome! Your appointment has been booked successfully! 

You'll receive a confirmation email shortly with all the details. Our technician will call you when they're on their way.

Thank you for choosing ${data?.companyName}! Is there anything else I can help you with?`,
        options: ["Book another service", "That's all, thanks!"],
      };

    default:
      return {
        content: "I'm not sure how to help with that. Let me start over.",
        options: ["Start booking process"],
      };
  }
};

// Helper function to get next step
export const getNextStep = (
  currentStep: ChatStep,
  response: string,
  data?: any
): ChatStep => {
  switch (currentStep) {
    case ChatStep.WELCOME:
      if (response.includes("start")) return ChatStep.VERIFY_DETAILS;
      return ChatStep.VERIFY_DETAILS; // Always proceed to verify details for known user flow

    case ChatStep.VERIFY_DETAILS:
      if (response.includes("Yes")) return ChatStep.SERVICE_TYPE;
      return ChatStep.ZIP_CODE; // If user wants to update, go to ZIP_CODE to start fresh

    case ChatStep.ZIP_CODE:
      return ChatStep.NAME;

    case ChatStep.NAME:
      return ChatStep.EMAIL;

    case ChatStep.EMAIL:
      return ChatStep.PHONE;

    case ChatStep.PHONE:
      return ChatStep.ADDRESS;

    case ChatStep.ADDRESS:
      return ChatStep.SERVICE_TYPE;

    case ChatStep.SERVICE_TYPE:
      return ChatStep.SERVICE_CATEGORY;

    case ChatStep.SERVICE_CATEGORY:
      // Check if there are specific services available
      const mainService =
        data?.service?.split(" - ")[0] || data?.selectedService;
      const subServiceResponse = response; // The response is the selected sub-service

      const serviceTypes = services[mainService as keyof typeof services];
      const specificServices =
        serviceTypes?.[subServiceResponse as keyof typeof serviceTypes];
      if (Array.isArray(specificServices) && specificServices.length > 0) {
        return ChatStep.SERVICE_SPECIFIC;
      }
      return ChatStep.SERVICE_DESCRIPTION;

    case ChatStep.SERVICE_SPECIFIC:
      return ChatStep.SERVICE_DESCRIPTION;

    case ChatStep.SERVICE_DESCRIPTION:
      return ChatStep.SCHEDULE_DATE;

    case ChatStep.SCHEDULE_DATE:
      return ChatStep.CONFIRMATION;

    case ChatStep.SCHEDULE_TIME:
      return ChatStep.CONFIRMATION;

    case ChatStep.CONFIRMATION:
      if (response.includes("book")) return ChatStep.COMPLETE;
      return ChatStep.ZIP_CODE; // Start over for changes

    case ChatStep.COMPLETE:
      if (response.includes("another")) return ChatStep.ZIP_CODE;
      return ChatStep.COMPLETE; // Stay on complete

    default:
      return ChatStep.WELCOME;
  }
};
