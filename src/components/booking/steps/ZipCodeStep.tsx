import { useState } from "react";
import { MapPinned } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ZipCodeStepProps {
  onComplete: (data: { zipCode: string }) => void;
  initialData?: { zipCode: string };
}

export function ZipCodeStep({ onComplete, initialData }: ZipCodeStepProps) {
  const [zipCode, setZipCode] = useState(initialData?.zipCode || "");
  const [error, setError] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!zipCode.trim()) {
      setError("Please enter your zip code");
      return;
    }

    setError("");
    onComplete({ zipCode: zipCode.trim() });
  };

  return (
    <div className="max-w-sm mx-auto">
      <div className="mb-2">
        <h2 className="text-lg font-bold text-foreground mb-2 flex items-center gap-2">
          <MapPinned className="w-5 h-5 text-primary" />
          Where are you?
        </h2>
        <p className="text-sm text-muted-foreground">
          Enter your zip code to check service availability.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-2">
        <div className="text-left">
          <Label htmlFor="zipCode" className="text-sm font-medium">
            Zip Code <span className="text-destructive">*</span>
          </Label>
          <Input
            id="zipCode"
            type="text"
            placeholder="Enter zip code"
            value={zipCode}
            onChange={(e) => {
              setZipCode(e.target.value);
              setError("");
            }}
            className={`mt-2 text-center ${error ? "border-destructive" : ""}`}
            maxLength={10}
          />
          {error && <p className="text-destructive text-sm mt-1">{error}</p>}
        </div>

        <Button
          type="submit"
          className="w-full bg-gradient-primary hover:opacity-90 transition-opacity"
          size="lg"
        >
          Continue
        </Button>
      </form>
    </div>
  );
}
