import { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, User, MapPin, Phone, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { services, serviceIcons, serviceColors } from "@/config/company";

export function ServiceStep({
  onNext,
  initialData,
  contactInfo,
  locationInfo,
  onEditContact,
  onEditLocation,
}: ServiceStepProps) {
  const subServiceRef = useRef<HTMLDivElement>(null);
  const specificServiceRef = useRef<HTMLDivElement>(null);
  const [selectedService, setSelectedService] = useState(
    initialData?.service || ""
  );
  const [selectedSubService, setSelectedSubService] = useState("");
  const [selectedSpecificService, setSelectedSpecificService] = useState("");
  const [description, setDescription] = useState(
    initialData?.description || ""
  );

  const handleServiceSelect = (serviceName: string) => {
    setSelectedService(serviceName);
    setSelectedSubService("");
    setSelectedSpecificService("");

    // Scroll to sub-service selection
    setTimeout(() => {
      if (subServiceRef.current) {
        subServiceRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  };

  const handleSubServiceSelect = (subServiceType: string) => {
    setSelectedSubService(subServiceType);
    setSelectedSpecificService("");

    // Scroll to specific service options
    setTimeout(() => {
      if (specificServiceRef.current) {
        specificServiceRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  };

  const handleNext = () => {
    if (!selectedService) return;

    let finalService = selectedService;
    if (selectedSubService && selectedSpecificService) {
      finalService = `${selectedService} - ${selectedSubService} - ${selectedSpecificService}`;
    } else if (selectedSubService) {
      finalService = `${selectedService} - ${selectedSubService}`;
    }

    console.log("ServiceStep: finalService", finalService);
    onNext({
      service: finalService,
      description: description.trim(),
    });
  };

  const canContinue = () => {
    if (!selectedService) return false;
    const subServices = services[selectedService as keyof typeof services];
    if (!subServices) return false;
    if (Object.keys(subServices).length > 0 && !selectedSubService)
      return false;

    const specificOptions =
      subServices[selectedSubService as keyof typeof subServices];
    if (
      Array.isArray(specificOptions) &&
      specificOptions.length > 0 &&
      !selectedSpecificService
    )
      return false;

    return true;
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="mb-2">
        <h2 className="text-lg font-bold text-foreground mb-2 flex items-center gap-2">
          <Wrench className="w-5 h-5 text-primary" />
          Select Service
        </h2>
        <p className="text-sm text-muted-foreground">
          What type of service do you need?
        </p>
      </div>

      {(contactInfo || locationInfo) && (
        <Card className="border-l-4 border-l-primary mb-3">
          <CardContent className="pb-0 py-1">
            <div className="flex justify-between items-center mb-1">
              <h3 className="font-semibold text-sm text-foreground">
                Contact Information
              </h3>
              {onEditContact && (
                <Button variant="ghost" size="icon" onClick={onEditContact}>
                  <Pencil className="w-4 h-4" />
                </Button>
              )}
            </div>
            {contactInfo && (
              <div className="flex flex-col">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-primary" />
                  <p className="text-sm text-muted-foreground">
                    {contactInfo.firstName} {contactInfo.lastName}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-primary" />
                  <p className="text-sm text-muted-foreground">
                    {contactInfo.phone}
                  </p>
                </div>
              </div>
            )}
            {locationInfo && (
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-primary" />
                <p className="text-sm text-muted-foreground">
                  {locationInfo.houseNumber} {locationInfo.street},{" "}
                  {locationInfo.city}, {locationInfo.zipCode}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 gap-3 mb-4">
        {Object.keys(services).map((serviceName) => {
          const Icon = serviceIcons[serviceName as keyof typeof serviceIcons];
          const isSelected = selectedService === serviceName;

          return (
            <Card
              key={serviceName}
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md border-2",
                isSelected
                  ? "border-primary bg-accent"
                  : "border-border hover:border-primary/30"
              )}
              onClick={() => handleServiceSelect(serviceName)}
            >
              <CardContent className="p-3 flex items-center gap-3">
                <div
                  className={cn(
                    "p-2 rounded-lg bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 shadow-sm",
                    serviceColors[serviceName as keyof typeof serviceColors]
                  )}
                >
                  <Icon className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-foreground text-sm">
                    {serviceName}
                  </h3>
                  <p className="text-xs text-muted-foreground">
                    Professional {serviceName.toLowerCase()} services
                  </p>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {selectedService &&
        services[selectedService as keyof typeof services] && // Added check here
        Object.keys(services[selectedService as keyof typeof services]).length >
          0 && (
          <div ref={subServiceRef} className="mb-2">
            <h3 className="text-sm font-medium text-foreground mb-2">
              Select service type
            </h3>
            <div className="flex flex-wrap gap-2 mb-3">
              {Object.keys(
                services[selectedService as keyof typeof services]
              ).map((subServiceType) => (
                <Button
                  key={subServiceType}
                  variant={
                    selectedSubService === subServiceType
                      ? "default"
                      : "outline"
                  }
                  onClick={() => handleSubServiceSelect(subServiceType)}
                >
                  {subServiceType}
                </Button>
              ))}
            </div>

            {selectedSubService &&
              selectedSubService !== "Other" &&
              Array.isArray(
                services[selectedService as keyof typeof services][
                  selectedSubService as keyof (typeof services)[keyof typeof services]
                ]
              ) && (
                <div ref={specificServiceRef}>
                  <h4 className="text-md font-semibold text-foreground mb-4">
                    What needs to be {selectedSubService.toLowerCase()}?
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {(
                      services[selectedService as keyof typeof services][
                        selectedSubService as keyof (typeof services)[keyof typeof services]
                      ] as string[]
                    ).map((option) => (
                      <Button
                        key={option}
                        variant={
                          selectedSpecificService === option
                            ? "default"
                            : "outline"
                        }
                        onClick={() => setSelectedSpecificService(option)}
                        size="sm"
                        className="text-xs py-2 px-3"
                      >
                        {option}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
          </div>
        )}

      <div className="mb-4">
        <label className="block text-sm font-medium text-foreground mb-2">
          Describe your issue (optional)
        </label>
        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Tell us more about what you need help with..."
          className="w-full min-h-24 p-3 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          rows={3}
        />
      </div>

      <div className="pt-2">
        <Button
          onClick={handleNext}
          disabled={!canContinue()}
          className="w-full"
        >
          Continue Booking
        </Button>
      </div>
    </div>
  );
}
