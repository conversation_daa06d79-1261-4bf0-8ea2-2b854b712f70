import { useState } from "react";
import { Calendar, Globe, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface ScheduleStepProps {
  onNext: (data: { date: Date; time: string }) => void;
  initialData?: { date: Date; time: string };
}

const timeSlots = [
  "08:00 - 10:00",
  "10:00 - 12:00",
  "12:00 - 14:00",
  "14:00 - 16:00",
  "16:00 - 18:00",
];

export function ScheduleStep({
  onNext,
  initialData,
}: ScheduleStepProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    initialData?.date
  );
  const [selectedTime, setSelectedTime] = useState(initialData?.time || "");
  const [selectedOption, setSelectedOption] = useState<
    "allAppointments" | "firstAvailable"
  >("firstAvailable");

  // Generate dynamic available slots starting from tomorrow
  const generateAvailableSlots = () => {
    const slots = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let i = 0; i < 5; i++) {
      // Generate 5 slots
      const date = new Date(today);
      date.setDate(today.getDate() + 1 + i); // Start from tomorrow
      const timeRange = timeSlots[i % timeSlots.length]; // Cycle through time slots
      slots.push({ date, timeRange });
    }
    return slots;
  };

  const availableSlots = generateAvailableSlots();

  const handleNext = () => {
    if (selectedOption === "firstAvailable") {
      // When "First available" is selected, selectedDate and selectedTime are already set by button click
      if (!selectedDate || !selectedTime) return; // Should not happen if a button is clicked
      onNext({
        date: selectedDate,
        time: selectedTime,
      });
    } else {
      if (!selectedDate || !selectedTime) return;
      onNext({
        date: selectedDate,
        time: selectedTime,
      });
    }
  };

  // Disable past dates
  const disabledDays = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="mb-2">
        <h2 className="text-lg font-bold text-foreground mb-2 flex items-center gap-2">
          <Calendar className="w-5 h-5 text-primary" />
          When do you need us?
        </h2>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-2">
        <Button
          variant={selectedOption === "firstAvailable" ? "default" : "outline"}
          onClick={() => setSelectedOption("firstAvailable")}
          className="flex-1"
        >
          First available
        </Button>
        <Button
          variant={selectedOption === "allAppointments" ? "default" : "outline"}
          onClick={() => setSelectedOption("allAppointments")}
          className="flex-1"
        >
          All Appointments
        </Button>
      </div>

      <div className="mb-2 text-sm text-muted-foreground">
        <h3 className="font-medium text-foreground mb-2 text-sm">Timezone</h3>
        <p className="flex items-center gap-2">
          <Globe className="w-4 h-4" />{" "}
          {Intl.DateTimeFormat().resolvedOptions().timeZone} (
          {new Date().toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          })}{" "}
          hr)
        </p>
      </div>

      {selectedOption === "firstAvailable" ? (
        <div className="space-y-2 mb-2">
          <h3 className="font-medium text-foreground mb-2 text-sm">
            Available Appointments
          </h3>
          <p className="text-xs text-muted-foreground mb-2">
            Service person will visit within the selected time range.
          </p>
          {/* Placeholder for 4-5 available appointments */}
          <div className="grid grid-cols-1 gap-2">
            {availableSlots.map((slot, index) => (
              <button
                key={index}
                className={cn(
                  "p-3 text-left rounded-lg border-2 transition-all duration-200",
                  selectedDate &&
                    format(selectedDate, "yyyy-MM-dd") ===
                      format(slot.date, "yyyy-MM-dd") &&
                    selectedTime === slot.timeRange
                    ? "border-primary bg-accent"
                    : "border-border bg-booking-card text-foreground hover:border-primary/30 hover:bg-accent/50"
                )}
                onClick={() => {
                  setSelectedDate(slot.date);
                  setSelectedTime(slot.timeRange);
                }}
              >
                <p>
                  {format(slot.date, "EEE, MMM do")}, {slot.timeRange}
                </p>
              </button>
            ))}
          </div>
          {selectedTime && ( // Conditional rendering for the second message
              <p className="text-xs text-muted-foreground mt-2">
                {" "}
                {/* Added mt-4 for spacing */}
                Please note: The actual service duration may vary and could
                extend beyond the selected time range.
              </p>
            )}
        </div>
      ) : (
        <div className="space-y-2 mb-2">
          {/* Calendar */}
          <div>
            <h3 className="font-medium text-foreground mb-2 text-sm">
              Select Date
            </h3>
            <div className="border border-border rounded-lg p-2 bg-booking-card">
              <CalendarComponent
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                disabled={disabledDays}
                className={cn("p-0 pointer-events-auto")}
              />
            </div>
            {selectedDate && (
              <p className="text-xs text-primary font-medium mt-1">
                Selected: {format(selectedDate, "MMM do, yyyy")}
              </p>
            )}
          </div>

          {/* Time Slots */}
          <div>
            <h3 className="font-medium text-foreground mb-2 text-sm">
              Select Time
            </h3>
            <p className="text-xs text-muted-foreground mb-2">
              Service person will visit within the selected time range.
            </p>
            <div className="grid grid-cols-2 gap-2">
              {timeSlots.map((time) => (
                <button
                  key={time}
                  onClick={() => setSelectedTime(time)}
                  disabled={!selectedDate}
                  className={cn(
                    "p-2 text-sm rounded-lg border-2 transition-all duration-200",
                    !selectedDate
                      ? "border-border bg-muted text-muted-foreground cursor-not-allowed"
                      : selectedTime === time
                      ? "border-primary bg-accent text-primary"
                      : "border-border bg-booking-card text-foreground hover:border-primary/30 hover:bg-accent/50"
                  )}
                >
                  {time}
                </button>
              ))}
            </div>
            {selectedTime && ( // Conditional rendering for the second message
              <p className="text-xs text-muted-foreground mt-2">
                {" "}
                {/* Added mt-4 for spacing */}
                Please note: The actual service duration may vary and could
                extend beyond the selected time range.
              </p>
            )}
          </div>
        </div>
      )}

      <div className="flex gap-2">
        <Button
          onClick={handleNext}
          disabled={
            selectedOption === "allAppointments" &&
            (!selectedDate || !selectedTime)
          }
          className="w-full bg-gradient-primary hover:opacity-90 transition-opacity"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
