import {
  CheckCircle,
  Calendar,
  Clock,
  MapPin,
  User,
  Wrench,
  Mail,
  Phone,
  Euro,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useState, useRef } from "react";
import { format } from "date-fns";
import {
  PlumbingIcon,
  ElectricalIcon,
  HVACIcon,
} from "@/components/icons/ServiceIcons";

interface ConfirmationData {
  zipCode: string;
  service: string;
  description: string;
  date: Date;
  time: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  houseNumber: string;
  street: string;
  city: string;
  notes: string;
}

import { Pencil, Paperclip } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";

interface ConfirmationStepProps {
  data: ConfirmationData;
  onBack: () => void;
  onConfirm: () => void;
  onEdit: (step: string) => void;
}

const serviceIcons: Record<string, any> = {
  Plumbing: PlumbingIcon,
  "Heating & Cooling": HVACIcon,
  Electrical: ElectricalIcon,
};

const serviceColors: Record<string, string> = {
  Plumbing: "text-purple-600",
  "Heating & Cooling": "text-purple-500",
  Electrical: "text-purple-700",
};

const getServiceIcon = (service: string | undefined | null) => {
  const mainService = (service || "").split(" - ")[0];
  return serviceIcons[mainService] || Wrench;
};

const getServiceColor = (service: string | undefined | null) => {
  const mainService = (service || "").split(" - ")[0];
  return serviceColors[mainService] || "text-gray-600";
};

const DISPATCH_FEE = 89;

export function ConfirmationStep({
  data,
  onBack,
  onConfirm,
  onEdit,
}: ConfirmationStepProps) {
  const [termsAccepted, setTermsAccepted] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUploadClick = () => {
    fileInputRef.current?.click();
  };

  function updateNotes(value: string): void {
    data.notes = value;
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-4">
        <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-booking-step-complete" />
        </div>
        <h2 className="text-2xl font-bold text-foreground mb-3">
          Confirm Your Appointment
        </h2>
        <p className="text-muted-foreground">
          Please review your booking details before confirming.
        </p>
      </div>

      <div className="space-y-3 mb-4">
        {/* Combined Booking Details */}
        <Card className="border-l-4 border-l-primary">
          <CardHeader className="pb-3 flex flex-row items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              Summary
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit("contact")}
            >
              <Pencil className="w-4 h-4 mr-2" />
            </Button>
          </CardHeader>
          <CardContent className="space-y-2">
            {/* Service Details */}
            <div className="flex items-start gap-2">
              {(() => {
                const ServiceIcon = getServiceIcon(data.service);
                return (
                  <ServiceIcon
                    className={`w-5 h-5 mt-1 ${getServiceColor(data.service)}`}
                  />
                );
              })()}
              <div>
                <span className="font-semibold">
                  {data.service?.split(" - ")[0] || "Service not selected"}
                </span>
                {data.service?.includes(" - ") && (
                  <div className="flex flex-wrap gap-1 mt-1">
                    {data.service
                      .split(" - ")
                      .slice(1)
                      .map((tag, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                  </div>
                )}
                {data.description && (
                  <p className="text-muted-foreground text-sm mt-1">
                    {data.description}
                  </p>
                )}
              </div>
            </div>

            <Separator className="my-2" />

            {/* Schedule Details */}
            <div className="flex items-start gap-2">
              <Calendar className="w-5 h-5 mt-1 text-primary" />
              <div>
                <span>
                  {data.date
                    ? typeof data.date === "string"
                      ? data.date
                      : format(data.date, "EEEE, MMMM do, yyyy")
                    : "Date not selected"}
                </span>
                <div className="flex items-center gap-2 text-muted-foreground text-sm mt-1">
                  {data.time}
                </div>
              </div>
            </div>

            <Separator className="my-2" />

            {/* Address Details */}
            <div className="flex items-start gap-2">
              <MapPin className="w-5 h-5 mt-1 text-primary" />
              <div>
                <span>Service Address</span>
                <div className="text-muted-foreground text-sm mt-1">
                  <div>
                    {data.houseNumber} {data.street}
                  </div>
                  <div>
                    {data.city}, {data.zipCode}
                  </div>
                </div>
              </div>
            </div>

            <Separator className="my-2" />

            {/* Contact Details */}
            <div className="flex items-start gap-2">
              <User className="w-5 h-5 mt-1 text-primary" />
              <div>
                <span>Contact Information</span>
                <div className="text-muted-foreground text-sm mt-1">
                  <div>
                    {data.firstName} {data.lastName}
                  </div>
                  <div className="flex items-center gap-2">{data.email}</div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4" /> {data.phone}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Details */}
        <Card className="border-l-4 border-l-primary">
          <CardHeader className="pb-3 flex flex-row items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              Additional Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Textarea
              id="notes"
              value={data.notes}
              onChange={updateNotes}
              className="resize-none min-h-[80px]"
              placeholder="Please add any additional details about your issue here"
            />
            <div className="mt-4">
              <input
                id="file-upload"
                type="file"
                multiple
                className="hidden"
                ref={fileInputRef}
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleFileUploadClick}
                className="w-full"
              >
                <Paperclip className="w-4 h-4 mr-2" /> Upload Supporting
                Documents
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dispatch Fee */}
      <Card className="border-l-4 border-l-primary mb-3">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Euro className="w-5 h-5 text-primary" />
            Service Fee
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Dispatch Fee:</span>
            <span className="font-bold text-lg">€{DISPATCH_FEE}</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            This covers the cost of dispatching a qualified technician to your
            location. Additional service charges may apply based on the work
            performed.
          </p>
        </CardContent>
      </Card>

      {/* Terms and Conditions */}
      <div className="mb-3 flex items-center space-x-2">
        <Checkbox
          id="terms"
          checked={termsAccepted}
          onCheckedChange={setTermsAccepted}
        />
        <Label
          htmlFor="terms"
          className="text-sm  leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          I agree to the{" "}
          <a
            href="#"
            className="underline underline-offset-4 hover:text-primary"
          >
            Terms and Conditions
          </a>
        </Label>
      </div>

      <div className="bg-accent rounded-lg p-6 mb-4">
        <h3 className="font-semibold text-foreground mb-2">
          What happens next?
        </h3>
        <ul className="space-y-2 text-sm text-muted-foreground">
          <li className="flex items-start gap-2">
            <CheckCircle className="w-4 h-4 text-booking-step-complete mt-0.5 flex-shrink-0" />
            You'll receive a confirmation email shortly
          </li>
          <li className="flex items-start gap-2">
            <CheckCircle className="w-4 h-4 text-booking-step-complete mt-0.5 flex-shrink-0" />
            Our team will call you to confirm the appointment details
          </li>
          <li className="flex items-start gap-2">
            <CheckCircle className="w-4 h-4 text-booking-step-complete mt-0.5 flex-shrink-0" />
            A qualified technician will arrive at your scheduled time
          </li>
        </ul>
      </div>

      <div className="flex">
        <Button
          onClick={onConfirm}
          className="w-full bg-gradient-primary hover:opacity-90 transition-opacity"
          size="lg"
          disabled={!termsAccepted}
        >
          Confirm Appointment
        </Button>
      </div>
    </div>
  );
}
