import { useState } from "react";
import { User, Mail, Phone, Home, CheckCircle2, Edit } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/hooks/use-toast";

interface KnownContactInfoData {
  name: string;
  email: string;
  phone: string;
  address: string;
}

interface KnownContactInfoStepProps {
  onNext: (data: KnownContactInfoData) => void;
  initialData: KnownContactInfoData;
}

export function KnownContactInfoStep({
  onNext,
  initialData,
}: KnownContactInfoStepProps) {
  const [formData, setFormData] = useState<KnownContactInfoData>(initialData);
  const [isEditing, setIsEditing] = useState(false);
  const [errors, setErrors] = useState<Partial<KnownContactInfoData>>({});

  const validateForm = () => {
    const newErrors: Partial<KnownContactInfoData> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (
      !/^[\+]?[\d]{1,16}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ""))
    ) {
      newErrors.phone = "Please enter a valid phone number";
    }

    if (!formData.address.trim()) {
      newErrors.address = "Address is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onNext(formData);
    } else {
      toast({
        title: "Validation Error",
        description: "Please correct the errors in the form.",
        variant: "destructive",
      });
    }
  };

  const updateField = (field: keyof KnownContactInfoData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="mb-4">
        <h2 className="text-lg font-bold text-foreground mb-2 flex items-center gap-2">
          <User className="w-5 h-5 text-primary" />
          Confirm Your Contact Information
        </h2>
        <p className="text-sm text-muted-foreground">
          Please confirm or update your details below.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="name" className="flex items-center gap-2">
            <User className="w-4 h-4" />
            Full Name <span className="text-destructive">*</span>
          </Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => updateField("name", e.target.value)}
            className={errors.name ? "border-destructive" : ""}
            placeholder="Enter full name"
            readOnly={!isEditing}
          />
          {errors.name && (
            <p className="text-destructive text-sm mt-1">{errors.name}</p>
          )}
        </div>

        <div>
          <Label htmlFor="email" className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            Email Address <span className="text-destructive">*</span>
          </Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => updateField("email", e.target.value)}
            className={errors.email ? "border-destructive" : ""}
            placeholder="Enter email address"
            readOnly={!isEditing}
          />
          {errors.email && (
            <p className="text-destructive text-sm mt-1">{errors.email}</p>
          )}
        </div>

        <div>
          <Label htmlFor="phone" className="flex items-center gap-2">
            <Phone className="w-4 h-4" />
            Phone Number <span className="text-destructive">*</span>
          </Label>
          <Input
            id="phone"
            type="tel"
            value={formData.phone}
            onChange={(e) => updateField("phone", e.target.value)}
            className={errors.phone ? "border-destructive" : ""}
            placeholder="Enter phone number"
            readOnly={!isEditing}
          />
          {errors.phone && (
            <p className="text-destructive text-sm mt-1">{errors.phone}</p>
          )}
        </div>

        <div>
          <Label htmlFor="address" className="flex items-center gap-2">
            <Home className="w-4 h-4" />
            Address <span className="text-destructive">*</span>
          </Label>
          <Input
            id="address"
            value={formData.address}
            onChange={(e) => updateField("address", e.target.value)}
            className={errors.address ? "border-destructive" : ""}
            placeholder="Enter full address"
            readOnly={!isEditing}
          />
          {errors.address && (
            <p className="text-destructive text-sm mt-1">{errors.address}</p>
          )}
        </div>

        <Separator className="my-6" />

        <div className="flex gap-2">
          {!isEditing ? (
            <Button
              type="button"
              onClick={() => setIsEditing(true)}
              variant="outline"
              className="w-full"
            >
              <Edit className="w-4 h-4 mr-2" /> Edit Details
            </Button>
          ) : (
            <Button
              type="button"
              onClick={() => {
                if (validateForm()) {
                  setIsEditing(false);
                  toast({
                    title: "Details Updated",
                    description: "Your contact details have been updated.",
                  });
                } else {
                  toast({
                    title: "Validation Error",
                    description: "Please correct the errors before confirming.",
                    variant: "destructive",
                  });
                }
              }}
              className="w-full"
            >
              <CheckCircle2 className="w-4 h-4 mr-2" /> Confirm Changes
            </Button>
          )}
          <Button
            type="submit"
            className="w-full bg-gradient-primary hover:opacity-90 transition-opacity"
          >
            Confirm and Continue
          </Button>
        </div>
      </form>
    </div>
  );
}
