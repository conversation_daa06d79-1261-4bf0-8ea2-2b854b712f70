import { cn } from "@/lib/utils";

interface CleanProgressBarProps {
  currentStep: number;
  totalSteps: number;
  steps: string[];
  showStepNames?: boolean;
  className?: string;
}

export function CleanProgressBar({
  currentStep,
  totalSteps,
  steps,
  showStepNames = false,
  className,
}: CleanProgressBarProps) {
  const progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100;

  return (
    <div className={cn("w-full py-4", className)}>
      {/* Step names (optional) */}
      {showStepNames && (
        <div className="flex justify-between mb-2">
          {steps.map((step, index) => {
            const stepNumber = index + 1;
            const isComplete = stepNumber < currentStep;
            const isCurrent = stepNumber === currentStep;

            return (
              <div
                key={index}
                className={cn(
                  "text-xs font-medium transition-colors duration-300",
                  {
                    "text-booking-step-complete": isComplete,
                    "text-booking-step": isCurrent,
                    "text-muted-foreground": stepNumber > currentStep,
                  }
                )}
              >
                {step}
              </div>
            );
          })}
        </div>
      )}

      {/* Progress bar container */}
      <div className="relative">
        {/* Background bar */}
        <div className="w-full h-2 bg-booking-step-inactive rounded-full overflow-hidden">
          {/* Progress fill */}
          <div
            className="h-full bg-gradient-to-r from-booking-step to-booking-step-complete rounded-full transition-all duration-500 ease-out"
            style={{
              width: `${Math.max(progressPercentage, 0)}%`,
            }}
          />
        </div>

        {/* Current step indicator */}
        <div
          className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-booking-step border-4 border-white dark:border-gray-800 rounded-full shadow-lg transition-all duration-500 ease-out"
          style={{
            left: `calc(${Math.max(progressPercentage, 0)}% - 8px)`,
          }}
        />
      </div>

      {/* Step counter */}
      <div className="flex justify-center mt-3">
        <span className="text-sm text-muted-foreground">
          Step {currentStep} of {totalSteps}
        </span>
      </div>
    </div>
  );
}

// Minimal version for popover (no step names, smaller size)
export function MinimalProgressBar({
  currentStep,
  totalSteps,
  className,
}: {
  currentStep: number;
  totalSteps: number;
  className?: string;
}) {
  const progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100;

  return (
    <div className={cn("w-full py-1", className)}>
      {/* Progress bar container */}
      <div className="relative">
        {/* Background bar */}
        <div className="w-full h-0.5 bg-booking-step-inactive rounded-full overflow-hidden">
          {/* Progress fill */}
          <div
            className="h-full bg-booking-step rounded-full transition-all duration-500 ease-out"
            style={{
              width: `${Math.max(progressPercentage, 0)}%`,
            }}
          />
        </div>
      </div>
    </div>
  );
}
