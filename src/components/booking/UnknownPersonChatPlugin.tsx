import { useState, useEffect, useRef } from "react";
import { useReward } from "react-rewards";
import { X, Send, MessageCircle, UserPlus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import { getChatFlow, getNextStep, ChatStep } from "./ChatFlow";
import { ChatConfirmationDisplay } from "./ChatConfirmationDisplay";

interface ChatMessage {
  id: string;
  type: "bot" | "user";
  content: string | React.ReactNode;
  timestamp: Date;
  options?: string[];
  inputType?: "text" | "email" | "tel" | "date" | "time";
  placeholder?: string;
  validation?: (value: string) => string | null;
  showTermsCheckbox?: boolean;
}

interface UnknownPersonChatPluginProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
  className?: string;
}

interface FloatingUnknownPersonChatButtonProps {
  onBookingComplete?: (data: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

// Chat Message Component
function ChatMessageComponent({
  message,
  onOptionSelect,
  onInputSubmit,
  onTermsAcceptedChange,
  termsAccepted,
}: {
  message: ChatMessage;
  onOptionSelect: (option: string) => void;
  onInputSubmit: (value: string) => void;
  onTermsAcceptedChange?: (accepted: boolean) => void;
  termsAccepted?: boolean;
}) {
  const [inputValue, setInputValue] = useState("");

  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    // Validate if validation function exists
    if (message.validation) {
      const error = message.validation(inputValue);
      if (error) {
        toast({
          title: "Invalid Input",
          description: error,
          variant: "destructive",
        });
        return;
      }
    }

    onInputSubmit(inputValue);
    setInputValue("");
  };

  if (message.type === "user") {
    return (
      <div className="flex justify-end">
        <div className="max-w-[80%] bg-primary text-primary-foreground rounded-lg px-3 py-2 text-sm">
          {message.content}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-start gap-2">
      <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
        <MessageCircle className="w-3 h-3 text-primary" />
      </div>
      <div className="flex-1">
        <div className="bg-muted rounded-lg px-3 py-2 text-sm text-foreground mb-2">
          {message.content}
        </div>

        {/* Option buttons */}
        {message.options && (
          <div className="flex flex-wrap gap-2">
            {message.options.map((option, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => onOptionSelect(option)}
                className="text-xs"
              >
                {option}
              </Button>
            ))}
          </div>
        )}

        {/* Input field for text responses */}
        {message.inputType && (
          <form onSubmit={handleInputSubmit} className="mt-2">
            <div className="flex gap-2">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={message.placeholder || "Type your response..."}
                type={message.inputType}
                className="flex-1 text-sm"
              />
              <Button type="submit" size="sm" disabled={!inputValue.trim()}>
                <Send className="w-3 h-3" />
              </Button>
            </div>
            {message.inputType === "tel" && message.showTermsCheckbox && (
              <div className="flex items-center space-x-2 mt-2">
                <Checkbox
                  id="terms"
                  checked={termsAccepted}
                  onCheckedChange={onTermsAcceptedChange}
                />
                <label
                  htmlFor="terms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I accept the{" "}
                  <a href="#" className="underline">
                    terms and conditions
                  </a>
                </label>
              </div>
            )}
          </form>
        )}
      </div>
    </div>
  );
}

export function UnknownPersonChatPlugin({
  isOpen,
  onClose,
  onComplete,
  className,
}: UnknownPersonChatPluginProps) {
  const companyConfig = useCompanyConfig();
  const { bookingData, updateBookingData, clearBookingData } =
    useBookingFormStorage();

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentStep, setCurrentStep] = useState<ChatStep>(ChatStep.WELCOME);
  const [isTyping, setIsTyping] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageIdCounter = useRef(0);
  const { reward } = useReward("unknown-chat-reward-id", "confetti", {
    elementCount: 200,
    startVelocity: 30,
    lifetime: 200,
  });

  // Auto-scroll to bottom when new messages are added
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize chat when opened
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      startConversation();
    }
  }, [isOpen]);

  const startConversation = () => {
    clearBookingData();
    setMessages([]);
    setCurrentStep(ChatStep.WELCOME);

    // Welcome message
    const welcomeMessage: ChatMessage = {
      id: (++messageIdCounter.current).toString(),
      type: "bot",
      content: `Hi! 👋 Welcome to ${companyConfig.name}! I'm here to help you book your service appointment. Let's start by getting your contact information.`,
      timestamp: new Date(),
    };

    setMessages([welcomeMessage]);

    // Start with phone input after a delay
    setTimeout(() => {
      const phoneMessage: ChatMessage = {
        id: (++messageIdCounter.current).toString(),
        type: "bot",
        content: "What's your phone number?",
        timestamp: new Date(),
        inputType: "tel",
        placeholder: "Enter your phone number",
        showTermsCheckbox: true,
      };

      setMessages((prev) => [...prev, phoneMessage]);
      setCurrentStep(ChatStep.PHONE);
    }, 1500);
  };

  const addBotMessage = (
    content: string | React.ReactNode,
    options?: string[],
    inputType?: ChatMessage["inputType"],
    placeholder?: string,
    validation?: (value: string) => string | null
  ) => {
    setIsTyping(true);

    setTimeout(() => {
      const message: ChatMessage = {
        id: (++messageIdCounter.current).toString(),
        type: "bot",
        content,
        timestamp: new Date(),
        options,
        inputType,
        placeholder,
        validation,
      };

      setMessages((prev) => [...prev, message]);
      setIsTyping(false);
    }, 1000);
  };

  const addUserMessage = (content: string) => {
    const message: ChatMessage = {
      id: (++messageIdCounter.current).toString(),
      type: "user",
      content,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, message]);
  };

  // Process user responses
  const processUserResponse = (response: string) => {
    let updatedData = { ...bookingData };

    switch (currentStep) {
      case ChatStep.PHONE:
        if (!termsAccepted) {
          toast({
            title: "Terms Required",
            description: "Please accept the terms and conditions to proceed.",
            variant: "destructive",
          });
          return;
        }
        updatedData.phone = response;
        addBotMessage(
          "Great! What's your full name?",
          undefined,
          "text",
          "Enter your full name"
        );
        setCurrentStep(ChatStep.NAME);
        break;

      case ChatStep.NAME:
        updatedData.name = response;
        updatedData.firstName = response.split(" ")[0];
        updatedData.lastName = response.split(" ").slice(1).join(" ");
        addBotMessage(
          `Thanks ${updatedData.firstName}! What's your email address?`,
          undefined,
          "email",
          "Enter your email address"
        );
        setCurrentStep(ChatStep.EMAIL);
        break;

      case ChatStep.EMAIL:
        updatedData.email = response;
        addBotMessage(
          "Perfect! What's your complete address?",
          undefined,
          "text",
          "Enter your complete address"
        );
        setCurrentStep(ChatStep.ADDRESS);
        break;

      case ChatStep.ADDRESS:
        updatedData.address = response;
        addBotMessage(
          "Excellent! Now let's get your service details. What type of service do you need?"
        );
        setCurrentStep(ChatStep.SERVICE_TYPE);
        break;

      default:
        // Handle service-related steps using the regular flow
        if (currentStep >= ChatStep.SERVICE_TYPE) {
          const nextStep = getNextStep(
            currentStep,
            response,
            updatedData,
            false
          );
          setCurrentStep(nextStep);

          const flow = getChatFlow(nextStep, {
            companyName: companyConfig.name,
            ...updatedData,
          });

          addBotMessage(
            flow.content,
            flow.options,
            flow.inputType,
            flow.placeholder,
            flow.validation
          );
        }
        break;
    }

    // Update booking data
    updateBookingData(updatedData);
  };

  const handleBookingComplete = (data: any) => {
    toast({
      title: "Booking Confirmed!",
      description: "Your appointment has been successfully scheduled.",
    });

    reward();
    onComplete?.(data);

    const flow = getChatFlow(ChatStep.COMPLETE, {
      companyName: companyConfig.name,
      ...data,
    });

    addBotMessage(flow.content, flow.options);
    setCurrentStep(ChatStep.COMPLETE);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
      <Card
        className={cn(
          "w-full max-w-md h-[600px] flex flex-col bg-white dark:bg-gray-800 shadow-2xl",
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-white text-gray-900 rounded-t-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <MessageCircle className="w-4 h-4" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{companyConfig.name}</h3>
              <p className="text-xs opacity-90">New Customer Chat</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-white hover:bg-white/20 h-8 w-8"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <ChatMessageComponent
              key={message.id}
              message={message}
              onOptionSelect={(option) => {
                addUserMessage(option);
                processUserResponse(option);
              }}
              onInputSubmit={(value) => {
                addUserMessage(value);
                processUserResponse(value);
              }}
              onTermsAcceptedChange={setTermsAccepted}
              termsAccepted={termsAccepted}
            />
          ))}

          {/* Typing indicator */}
          {isTyping && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                <MessageCircle className="w-3 h-3 text-primary" />
              </div>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" />
                <div
                  className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                />
                <div
                  className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                />
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Confirmation display for confirmation step */}
        {currentStep === ChatStep.CONFIRMATION && (
          <div className="p-4 border-t">
            <ChatConfirmationDisplay
              data={bookingData}
              onTermsAcceptedChange={setTermsAccepted}
            />
          </div>
        )}

        {/* Reward animation */}
        <div
          id="unknown-chat-reward-id"
          className="absolute top-1/2 left-1/2"
        />
      </Card>
    </div>
  );
}

// Floating Button Component
export function FloatingUnknownPersonChatButton({
  onBookingComplete,
  onBookingStart,
  className,
}: FloatingUnknownPersonChatButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className={cn("fixed bottom-20 right-6 z-40", className)}>
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-16 h-16 rounded-full bg-gray-900 hover:bg-gray-800 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 group p-0"
        >
          <div className="flex flex-col items-center">
            <UserPlus className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span className="text-xs font-medium">New Chat</span>
          </div>
        </Button>
      </div>

      <UnknownPersonChatPlugin
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
          setIsOpen(false);
        }}
      />
    </>
  );
}
