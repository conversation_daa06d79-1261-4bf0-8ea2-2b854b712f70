import { useState, useEffect, useRef } from "react";
import { useReward } from "react-rewards";
import {
  X,
  Send,
  Calendar,
  User,
  MapPin,
  MessageCircle,
  Wrench,
  RotateCcw,
  CheckCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox"; // New import
import { cn } from "@/lib/utils";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import { getChatFlow, getNextStep, ChatStep, validators } from "./ChatFlow";
import { ChatConfirmationDisplay } from "./ChatConfirmationDisplay";
import { findUserByPhone } from "@/data/knownUsers";

// Known user details
const KNOWN_USER = {
  firstName: "<PERSON>",
  lastName: "<PERSON>",
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "0987654321",
  address: "Rosenhof 12, 8002 Zurich",
  zipCode: "8002",
};

interface ChatMessage {
  id: string;
  type: "bot" | "user";
  content: string | React.ReactNode;
  timestamp: Date;
  options?: string[];
  inputType?: "text" | "email" | "tel" | "date" | "time";
  placeholder?: string;
  validation?: (value: string) => string | null;
  showTermsCheckbox?: boolean; // New property
}

interface KnownUserChatPluginProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
  className?: string;
}

interface FloatingKnownUserChatButtonProps {
  onBookingComplete?: (data: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

// Chat Message Component - exactly like the regular ChatPlugin
function ChatMessageComponent({
  message,
  onOptionSelect,
  onInputSubmit,
  onTermsAcceptedChange, // New prop
  termsAccepted, // New prop
  onInvalidInput, // New prop
}: {
  message: ChatMessage;
  onOptionSelect: (option: string) => void;
  onInputSubmit: (value: string) => void;
  onTermsAcceptedChange: (checked: boolean) => void; // New prop
  termsAccepted: boolean; // New prop
  onInvalidInput: (error: string) => void; // New prop
}) {
  const [inputValue, setInputValue] = useState("");

  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    // Validate if validation function exists
    if (message.validation) {
      const error = message.validation(inputValue);
      if (error) {
        onInvalidInput(error);
        return;
      }
    }

    onInputSubmit(inputValue);
    setInputValue("");
  };

  if (message.type === "user") {
    return (
      <div className="flex justify-end">
        <div className="max-w-[80%] bg-primary text-primary-foreground rounded-lg px-3 py-2 text-sm">
          {message.content}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-start gap-2">
      <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
        <MessageCircle className="w-3 h-3 text-primary" />
      </div>
      <div className="flex-1">
        <div className="bg-muted rounded-lg px-3 py-2 text-sm text-foreground mb-2">
          {message.content}
        </div>

        {/* Option buttons */}
        {message.options && (
          <div className="flex flex-wrap gap-2">
            {message.options.map((option, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => onOptionSelect(option)}
                className="text-xs"
              >
                {option}
              </Button>
            ))}
          </div>
        )}

        {/* Input field for text responses */}
        {message.inputType && (
          <form onSubmit={handleInputSubmit} className="mt-2">
            <div className="flex gap-2">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={message.placeholder || "Type your response..."}
                type={message.inputType}
                className="flex-1 text-sm"
              />
              <Button
                type="submit"
                size="sm"
                disabled={
                  !inputValue.trim() ||
                  (message.inputType === "tel" &&
                    message.showTermsCheckbox &&
                    !termsAccepted)
                }
              >
                <Send className="w-3 h-3" />
              </Button>
            </div>
            {message.inputType === "tel" && message.showTermsCheckbox && (
              <div className="flex items-center space-x-2 mt-2">
                <Checkbox
                  id="terms"
                  checked={termsAccepted}
                  onCheckedChange={onTermsAcceptedChange}
                />
                <label
                  htmlFor="terms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I accept the{" "}
                  <a href="#" className="underline">
                    terms and conditions
                  </a>
                </label>
              </div>
            )}
          </form>
        )}
      </div>
    </div>
  );
}

export function KnownUserChatPlugin({
  isOpen,
  onClose,
  onComplete,
  className,
}: KnownUserChatPluginProps) {
  const companyConfig = useCompanyConfig();
  const { bookingData, updateBookingData, clearBookingData } =
    useBookingFormStorage();

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentStep, setCurrentStep] = useState<ChatStep>(ChatStep.WELCOME); // Start from WELCOME
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [selectedService, setSelectedService] = useState("");
  const [selectedSubService, setSelectedSubService] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [foundUser, setFoundUser] = useState<any>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messageIdCounter = useRef(0);
  const { reward, isAnimating } = useReward(
    "known-chat-reward-id",
    "confetti",
    {
      elementCount: 200,
      startVelocity: 30,
      lifetime: 200,
    }
  );

  const [inChatNotification, setInChatNotification] = useState<{
    title: string;
    description: string;
    variant?: "default" | "destructive";
  } | null>(null);
  const [animateNotification, setAnimateNotification] = useState(false);

  // Auto-scroll to bottom when new messages are added
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Clear in-chat notification after a delay
  useEffect(() => {
    if (inChatNotification) {
      const timer = setTimeout(() => {
        setInChatNotification(null);
        setAnimateNotification(false); // Reset animation state
      }, 5000); // Clear after 5 seconds
      return () => clearTimeout(timer);
    }
  }, [inChatNotification]);

  // Initialize chat when opened
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      startConversation();
    }
  }, [isOpen]);

  const startConversation = () => {
    clearBookingData(); // Clear any previous booking data
    setMessages([]); // Clear previous messages

    // Start with welcome message
    const welcomeFlow = getChatFlow(ChatStep.WELCOME, {
      companyName: companyConfig.name,
      firstName: KNOWN_USER.firstName,
    });

    const welcomeMessage: ChatMessage = {
      id: (++messageIdCounter.current).toString(),
      type: "bot",
      content: welcomeFlow.content || "",
      timestamp: new Date(),
      options: welcomeFlow.options,
    };

    setMessages([welcomeMessage]);
    setCurrentStep(ChatStep.WELCOME); // Set current step to WELCOME initially

    // Immediately ask for phone number after welcome
    addBotMessage(
      "Before we proceed, please confirm your phone number. This helps us ensure we have the correct contact information for your booking.",
      undefined,
      "tel",
      "Enter your phone number...",
      validators.phone,
      true // showTermsCheckbox
    );
    setCurrentStep(ChatStep.PHONE); // Move to PHONE step
  };

  const addBotMessage = (
    content: string | React.ReactNode,
    options?: string[],
    inputType?: ChatMessage["inputType"],
    placeholder?: string,
    validation?: (value: string) => string | null,
    showTermsCheckbox?: boolean // New parameter
  ) => {
    setIsTyping(true);

    setTimeout(() => {
      const message: ChatMessage = {
        id: (++messageIdCounter.current).toString(),
        type: "bot",
        content,
        timestamp: new Date(),
        options,
        inputType,
        placeholder,
        validation,
        showTermsCheckbox, // Pass the new parameter
      };

      setMessages((prev) => [...prev, message]);
      setIsTyping(false);
    }, 1000); // Simulate typing delay
  };

  const addUserMessage = (content: string) => {
    const message: ChatMessage = {
      id: (++messageIdCounter.current).toString(),
      type: "user",
      content,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, message]);
    setInputValue("");
  };

  // Handle option selection
  const handleOptionSelect = (option: string) => {
    addUserMessage(option);
    processUserResponse(option);
  };

  // Handle text input submission
  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    addUserMessage(inputValue);
    processUserResponse(inputValue);
  };

  // Process user responses and advance conversation - exactly like regular ChatPlugin
  const processUserResponse = (response: string) => {
    let updatedData = { ...bookingData }; // Use 'let' to allow re-assignment

    switch (currentStep) {
      case ChatStep.WELCOME:
        // This case should ideally not be hit for user input, as startConversation
        // immediately transitions to PHONE after WELCOME message.
        // However, if it is hit, we can just transition to PHONE.
        addBotMessage(
          "Please confirm your phone number to proceed.",
          undefined,
          "tel",
          "Enter your phone number...",
          validators.phone
        );
        setCurrentStep(ChatStep.PHONE);
        break;

      case ChatStep.PHONE:
        if (!termsAccepted) {
          setInChatNotification({
            title: "Terms Required",
            description: "Please accept the terms and conditions to proceed.",
            variant: "destructive",
          });
          setAnimateNotification(true);
          return;
        }
        updatedData.phone = response;

        // Check if this is a known user
        const user = findUserByPhone(response);
        if (user) {
          setFoundUser(user);
          updatedData = {
            ...updatedData,
            ...user,
            phone: response,
          };
          updateBookingData(updatedData);

          const verifyDetailsFlow = getChatFlow(
            ChatStep.VERIFY_DETAILS,
            updatedData
          );
          addBotMessage(verifyDetailsFlow.content, verifyDetailsFlow.options);
          setCurrentStep(ChatStep.VERIFY_DETAILS);
        } else {
          // Unknown user - collect information
          addBotMessage("Thank you! What's your full name?");
          setCurrentStep(ChatStep.NAME);
        }
        break;

      case ChatStep.NAME:
        updatedData.name = response;
        addBotMessage("Great! What's your email address?");
        setCurrentStep(ChatStep.EMAIL);
        break;

      case ChatStep.EMAIL:
        updatedData.email = response;
        addBotMessage("Perfect! What's your street address?");
        setCurrentStep(ChatStep.ADDRESS);
        break;

      case ChatStep.ADDRESS:
        updatedData.address = response;
        addBotMessage("What's your city?");
        setCurrentStep(ChatStep.CITY);
        break;

      case ChatStep.CITY:
        updatedData.city = response;
        addBotMessage("What's your zip code?");
        setCurrentStep(ChatStep.ZIP_CODE);
        break;

      case ChatStep.ZIP_CODE:
        updatedData.zipCode = response;
        updateBookingData(updatedData);
        addBotMessage(
          "Thank you! Now let's proceed with booking your service."
        );
        setCurrentStep(ChatStep.SERVICE_TYPE);
        const serviceTypeFlow = getChatFlow(ChatStep.SERVICE_TYPE, updatedData);
        addBotMessage(serviceTypeFlow.content, serviceTypeFlow.options);
        break;

      case ChatStep.VERIFY_DETAILS:
        if (response.includes("Yes")) {
          // Data is already updated from the PHONE step, just proceed
          addBotMessage("Great! Let's proceed with booking your service.");
          setCurrentStep(ChatStep.SERVICE_TYPE); // Move to SERVICE_TYPE
          const serviceTypeFlow = getChatFlow(
            ChatStep.SERVICE_TYPE,
            updatedData
          );
          addBotMessage(serviceTypeFlow.content, serviceTypeFlow.options);
        } else {
          // User wants to update, go back to PHONE to re-enter details
          clearBookingData(); // Clear previous data to start fresh
          addBotMessage(
            "No problem! Let's get your updated phone number first.",
            undefined,
            "tel",
            "Enter your phone number...",
            validators.phone
          );
          setCurrentStep(ChatStep.PHONE);
        }
        break;
      // Skip all the personal info steps since we already have them
      case ChatStep.SERVICE_TYPE: {
        // Added block scope
        setSelectedService(response);
        updatedData.service = response;
        addBotMessage(`You selected ${updatedData.service}.`);
        // Add next bot message for SERVICE_CATEGORY
        const nextStep = getNextStep(currentStep, response, {
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        const flow = getChatFlow(nextStep, {
          companyName: companyConfig.name,
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        addBotMessage(
          flow.content,
          flow.options,
          flow.inputType,
          flow.placeholder,
          flow.validation
        );
        setCurrentStep(nextStep);
        break;
      } // Closed block scope
      case ChatStep.SERVICE_CATEGORY: {
        // Added block scope
        setSelectedSubService(response);
        updatedData.service = `${selectedService} - ${response}`;
        addBotMessage(
          `Understood. You need ${response} for ${selectedService}.`
        );
        // Add next bot message for SERVICE_SPECIFIC or SERVICE_DESCRIPTION
        const nextStep = getNextStep(currentStep, response, {
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        const flow = getChatFlow(nextStep, {
          companyName: companyConfig.name,
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        addBotMessage(
          flow.content,
          flow.options,
          flow.inputType,
          flow.placeholder,
          flow.validation
        );
        setCurrentStep(nextStep);
        break;
      } // Closed block scope
      case ChatStep.SERVICE_SPECIFIC: {
        // Added block scope
        updatedData.service = `${selectedService} - ${selectedSubService} - ${response}`;
        addBotMessage(`Okay, specifically ${response}.`);
        // Add next bot message for SERVICE_DESCRIPTION
        const nextStep = getNextStep(currentStep, response, {
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        const flow = getChatFlow(nextStep, {
          companyName: companyConfig.name,
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        addBotMessage(
          flow.content,
          flow.options,
          flow.inputType,
          flow.placeholder,
          flow.validation
        );
        setCurrentStep(nextStep);
        break;
      } // Closed block scope
      case ChatStep.SERVICE_DESCRIPTION: {
        // Added block scope
        updatedData.description = ""; // No text input for description
        addBotMessage("Okay, moving on.");
        // Add next bot message for SCHEDULE_DATE
        const nextStep = getNextStep(currentStep, response, {
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        const flow = getChatFlow(nextStep, {
          companyName: companyConfig.name,
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        addBotMessage(
          flow.content,
          flow.options,
          flow.inputType,
          flow.placeholder,
          flow.validation
        );
        setCurrentStep(nextStep);
        break;
      } // Closed block scope
      case ChatStep.SCHEDULE_DATE: {
        // Added block scope
        const parts = response.split(", ");
        const dateString = `${parts[0]}, ${parts[1]} ${parts[2]}, ${parts[3]}`; // "Mon, Aug 28, 2025"
        const timeRange = parts[4]; // "08:00 - 10:00"

        updatedData.date = dateString; // Store as string
        updatedData.time = timeRange;
        addBotMessage(
          `You'd like to schedule for ${dateString} at ${timeRange}.`
        );
        // Add next bot message for CONFIRMATION
        const nextStep = getNextStep(currentStep, response, {
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        const flow = getChatFlow(nextStep, {
          companyName: companyConfig.name,
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        // Special handling for Confirmation Step
        if (nextStep === ChatStep.CONFIRMATION) {
          flow.content = (
            <ChatConfirmationDisplay
              data={updatedData}
              onTermsAcceptedChange={(checked) => setTermsAccepted(checked)}
            />
          );
        }
        addBotMessage(
          flow.content,
          flow.options,
          flow.inputType,
          flow.placeholder,
          flow.validation
        );
        setCurrentStep(nextStep);
        break;
      } // Closed block scope
      case ChatStep.SCHEDULE_TIME: {
        // Added block scope
        updatedData.time = response;
        addBotMessage(`And the best time is ${updatedData.time}.`);
        // Add next bot message for CONFIRMATION
        const nextStep = getNextStep(currentStep, response, {
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        const flow = getChatFlow(nextStep, {
          companyName: companyConfig.name,
          selectedService,
          selectedSubService,
          ...updatedData,
        });
        // Special handling for Confirmation Step
        if (nextStep === ChatStep.CONFIRMATION) {
          flow.content = (
            <ChatConfirmationDisplay
              data={updatedData}
              onTermsAcceptedChange={(checked) => setTermsAccepted(checked)}
            />
          );
        }
        addBotMessage(
          flow.content,
          flow.options,
          flow.inputType,
          flow.placeholder,
          flow.validation
        );
        setCurrentStep(nextStep);
        break;
      } // Closed block scope
      case ChatStep.CONFIRMATION:
        if (response.includes("book")) {
          if (!termsAccepted) {
            toast({
              title: "Terms Required",
              description: "Please accept the terms and conditions to proceed.",
              variant: "destructive",
            });
            return;
          }
          // Complete the booking
          handleBookingComplete(updatedData);
          return;
        } else {
          // Start over for changes
          setMessages([]);
          setCurrentStep(ChatStep.WELCOME); // Go back to WELCOME to restart the flow
          startConversation();
          return;
        }
      case ChatStep.COMPLETE:
        if (response.includes("another")) {
          // Start new booking
          clearBookingData();
          setMessages([]);
          setCurrentStep(ChatStep.WELCOME);
          startConversation();
          return;
        } else if (response.includes("That's all, thanks!")) {
          // Explicitly check for "That's all, thanks!"
          onComplete?.(bookingData);
          return;
        } else {
          // Default behavior if response is not "another" or "That's all, thanks!"
          // This might happen if there are other options or unexpected input
          onComplete?.(bookingData);
          return;
        }
    }

    updateBookingData(updatedData);
  };

  const handleBookingComplete = (data: any) => {
    setInChatNotification({
      title: "Booking Confirmed!",
      description:
        "Your appointment has been successfully scheduled. You'll receive a confirmation email shortly.",
    });
    setAnimateNotification(true);

    reward(); // Trigger confetti animation

    onComplete?.(data);

    // Show completion message
    const flow = getChatFlow(ChatStep.COMPLETE, {
      companyName: companyConfig.name,
      ...data,
    });

    addBotMessage(flow.content, flow.options);
    setCurrentStep(ChatStep.COMPLETE);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-end p-4 bg-gray-100">
      <Card
        className={cn(
          "w-full max-w-md max-h-[90vh] flex flex-col bg-white dark:bg-gray-800 shadow-2xl", // Changed h-[600px] to max-h-[90vh]
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-purple-800 to-purple-900 text-white rounded-t-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <MessageCircle className="w-4 h-4" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{companyConfig.name}</h3>
              <p className="text-xs opacity-90">Book online now</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-white hover:bg-white/20 h-8 w-8"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {inChatNotification && (
          <div
            className={cn(
              "p-2 text-sm text-center transition-all duration-300 ease-out",
              inChatNotification.variant === "destructive"
                ? "bg-red-100 text-red-800"
                : "bg-green-100 text-green-800",
              animateNotification
                ? "opacity-100 translate-y-0"
                : "opacity-0 -translate-y-full"
            )}
          >
            <p className="font-semibold">{inChatNotification.title}</p>
            <p>{inChatNotification.description}</p>
          </div>
        )}

        {/* Messages - exactly like regular ChatPlugin */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <ChatMessageComponent
              key={message.id}
              message={message}
              onOptionSelect={handleOptionSelect}
              onInputSubmit={(value) => {
                addUserMessage(value);
                processUserResponse(value);
              }}
              onTermsAcceptedChange={setTermsAccepted} // Pass the setter
              termsAccepted={termsAccepted} // Pass the state
              onInvalidInput={(error) => {
                setInChatNotification({
                  title: "Invalid Input",
                  description: error,
                  variant: "destructive",
                });
                setAnimateNotification(true);
              }} // Handle invalid input from ChatMessageComponent
            />
          ))}

          {/* Typing indicator */}
          {isTyping && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                <MessageCircle className="w-3 h-3 text-primary" />
              </div>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" />
                <div
                  className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                />
                <div
                  className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                />
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Always visible input area - exactly like regular ChatPlugin */}
        <form onSubmit={handleInputSubmit} className="p-4 border-t">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Type your message..."
              className="flex-1"
              autoFocus
            />
            <button
              type="submit"
              className="w-10 h-10 rounded-full p-0 flex-grow-0 flex-shrink-0 inline-flex items-center justify-center bg-primary text-primary-foreground hover:bg-primary/90 transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
              disabled={!inputValue.trim()}
            >
              <Send className="w-4 h-4" />
            </button>
          </div>
        </form>

        {/* Reward animation */}
        <div
          id="known-chat-reward-id"
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
        />
      </Card>
    </div>
  );
}

// Floating Chat Button Component for Known Users - exactly like regular ChatPlugin
export function FloatingKnownUserChatButton({
  onBookingComplete,
  onBookingStart,
  className,
}: FloatingKnownUserChatButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className={cn("fixed bottom-6 left-6 z-40", className)}>
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-14 h-14 rounded-full bg-gradient-to-r from-purple-800 to-purple-900 hover:from-purple-900 hover:to-purple-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 group p-0"
        >
          <div className="flex flex-col items-center">
            <CheckCircle className="w-4 h-4 group-hover:scale-110 transition-transform" />
            <span className="text-xs font-medium">Known</span>
          </div>
        </Button>
      </div>

      <KnownUserChatPlugin
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
        }}
      />
    </>
  );
}
