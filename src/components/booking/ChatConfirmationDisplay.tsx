import React, { useState } from "react";
import {
  CheckCircle,
  Calendar,
  MapPin,
  User,
  Wrench,
  Mail,
  Phone,
  Euro,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils"; // Assuming cn is needed for styling
import {
  PlumbingIcon,
  ElectricalIcon,
  HVACIcon,
} from "@/components/icons/ServiceIcons";

// Re-use the BookingFormData interface from use-local-storage.ts
import { BookingFormData } from "@/hooks/use-local-storage";

interface ChatConfirmationDisplayProps {
  data: BookingFormData;
  onTermsAcceptedChange: (accepted: boolean) => void;
}

const serviceIcons: Record<string, any> = {
  Plumbing: PlumbingIcon,
  "Heating & Cooling": HVACIcon,
  Electrical: ElectricalIcon,
};

const serviceColors: Record<string, string> = {
  Plumbing: "text-purple-600",
  "Heating & Cooling": "text-purple-500",
  Electrical: "text-purple-700",
};

const getServiceIcon = (service: string) => {
  const mainService = service.split(" - ")[0];
  return serviceIcons[mainService] || Wrench;
};

const getServiceColor = (service: string) => {
  const mainService = service.split(" - ")[0];
  return serviceColors[mainService] || "text-gray-600";
};

const DISPATCH_FEE = 89;

export function ChatConfirmationDisplay({
  data,
  onTermsAcceptedChange,
}: ChatConfirmationDisplayProps) {
  const [termsAccepted, setTermsAccepted] = useState(false);

  const handleTermsChange = (checked: boolean) => {
    setTermsAccepted(checked);
    onTermsAcceptedChange(checked);
  };

  // Helper to format date and time
  const formattedDate = data.date || "Not provided";
  const formattedTime = data.time || "Not provided";

  return (
    <div className="space-y-4">
      <p className="text-muted-foreground text-sm mb-4">
        Please review your booking details before confirming.
      </p>

      {/* Combined Booking Details */}
      <Card className="border-l-4 border-l-primary">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center gap-2">
            Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-1">
          {/* Service Details */}
          <div className="flex items-start gap-2">
            {(() => {
              const ServiceIcon = getServiceIcon(data.service || "");
              return (
                <ServiceIcon
                  className={`w-5 h-5 mt-1 ${getServiceColor(
                    data.service || ""
                  )}`}
                />
              );
            })()}
            <div>
              <span className="font-semibold">
                {data.service?.split(" - ")[0] || "Not provided"}
              </span>
              {data.service?.includes(" - ") && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {data.service
                    .split(" - ")
                    .slice(1)
                    .map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="text-xs"
                      >
                        {tag}
                      </Badge>
                    ))}
                </div>
              )}
              {data.description && (
                <p className="text-muted-foreground text-sm mt-1">
                  {data.description}
                </p>
              )}
            </div>
          </div>

          <Separator className="my-2" />

          {/* Schedule Details */}
          <div className="flex items-start gap-2">
            <Calendar className="w-5 h-5 mt-1 text-primary" />
            <div>
              <span>{formattedDate}</span>
              <div className="flex items-center gap-2 text-muted-foreground text-sm mt-1">
                {formattedTime}
              </div>
            </div>
          </div>

          <Separator className="my-2" />

          {/* Address Details */}
          <div className="flex items-start gap-2">
            <MapPin className="w-5 h-5 mt-1 text-primary" />
            <div>
              <span>Service Address</span>
              <div className="text-muted-foreground text-sm mt-1">
                <div>{data.address || "Not provided"}</div>
                <div>{data.zipCode || "Not provided"}</div>
              </div>
            </div>
          </div>

          <Separator className="my-2" />

          {/* Contact Details */}
          <div className="flex items-start gap-2">
            <User className="w-5 h-5 mt-1 text-primary" />
            <div>
              <span>Contact Information</span>
              <div className="text-muted-foreground text-sm mt-1">
                <div>{data.name || "Not provided"}</div>
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" /> {data.email || "Not provided"}
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4" /> {data.phone || "Not provided"}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dispatch Fee */}
      <Card className="border-l-4 border-l-primary">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center gap-2">
            <Euro className="w-5 h-5 text-primary" />
            Service Fee
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Dispatch Fee:</span>
            <span className="font-bold text-lg">€{DISPATCH_FEE}</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            This covers the cost of dispatching a qualified technician to your
            location. Additional service charges may apply based on the work
            performed.
          </p>
        </CardContent>
      </Card>

      {/* Terms and Conditions */}
      <div className="mb-4 flex items-center space-x-2">
        <Checkbox
          id="terms"
          checked={termsAccepted}
          onCheckedChange={handleTermsChange}
        />
        <Label
          htmlFor="terms"
          className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          I agree to the{" "}
          <a
            href="#"
            className="underline underline-offset-4 hover:text-primary"
          >
            Terms and Conditions
          </a>
        </Label>
      </div>

      {/* What happens next? */}
      <div className="bg-accent rounded-lg p-4">
        {" "}
        {/* Reduced padding from p-6 to p-4 */}
        <h3 className="font-semibold text-foreground mb-2">
          What happens next?
        </h3>
        <ul className="space-y-1 text-sm text-muted-foreground">
          {" "}
          {/* Reduced space-y from space-y-2 to space-y-1 */}
          <li className="flex items-start gap-2">
            <CheckCircle className="w-4 h-4 text-booking-step-complete mt-0.5 flex-shrink-0" />
            You'll receive a confirmation email shortly
          </li>
          <li className="flex items-start gap-2">
            <CheckCircle className="w-4 h-4 text-booking-step-complete mt-0.5 flex-shrink-0" />
            Our team will call you to confirm the appointment details
          </li>
          <li className="flex items-start gap-2">
            <CheckCircle className="w-4 h-4 text-booking-step-complete mt-0.5 flex-shrink-0" />
            A qualified technician will arrive at your scheduled time
          </li>
        </ul>
      </div>
    </div>
  );
}
