import { useState, useEffect, useRef } from "react";
import { useReward } from "react-rewards";
import {
  X,
  Send,
  Calendar,
  User,
  MapPin,
  MessageCircle,
  Wrench,
  RotateCcw, // Import for Redo icon
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import { getChatFlow, getNextStep, ChatStep } from "./ChatFlow";
import { ChatConfirmationDisplay } from "./ChatConfirmationDisplay";

interface ChatMessage {
  id: string;
  type: "bot" | "user";
  content: string | React.ReactNode;
  timestamp: Date;
  options?: string[];
  inputType?: "text" | "email" | "tel" | "date" | "time";
  placeholder?: string;
  validation?: (value: string) => string | null;
}

interface ChatPluginProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
  className?: string;
}

interface FloatingChatButtonProps {
  onBookingComplete?: (data: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

// Chat Message Component
function ChatMessageComponent({
  message,
  onOptionSelect,
}: {
  message: ChatMessage;
  onOptionSelect: (option: string) => void;
}) {
  if (message.type === "user") {
    return (
      <div className="flex justify-end">
        <div className="max-w-[80%] bg-primary text-primary-foreground rounded-lg px-3 py-2 text-sm">
          {message.content}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-start gap-2">
      <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
        <MessageCircle className="w-3 h-3 text-primary" />
      </div>
      <div className="flex-1">
        {
          typeof message.content === "string"
            ? message.content && (
                <div className="bg-muted rounded-lg px-3 py-2 text-sm text-foreground mb-2">
                  {message.content}
                </div>
              )
            : message.content // Render ReactNode directly
        }

        {/* Option buttons */}
        {message.options && (
          <div className="flex flex-wrap gap-2">
            {message.options.map((option, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => onOptionSelect(option)}
                className="text-xs"
              >
                {option}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export function ChatPlugin({
  isOpen,
  onClose,
  onComplete,
  className,
}: ChatPluginProps) {
  const companyConfig = useCompanyConfig();
  const { bookingData, updateBookingData, clearBookingData } =
    useBookingFormStorage();

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentStep, setCurrentStep] = useState<ChatStep>(ChatStep.ZIP_CODE); // Start directly with ZIP_CODE
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [selectedService, setSelectedService] = useState("");
  const [selectedSubService, setSelectedSubService] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [inChatNotification, setInChatNotification] = useState<{ title: string; description: string; variant?: "default" | "destructive" } | null>(null);
  const [animateNotification, setAnimateNotification] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messageIdCounter = useRef(0);
  const { reward, isAnimating } = useReward("chat-reward-id", "confetti", {
    elementCount: 200,
    startVelocity: 30,
    lifetime: 200,
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Auto-focus on input when new bot message requires input
  useEffect(() => {
    if (
      inputRef.current &&
      messages.length > 0 &&
      messages[messages.length - 1]?.inputType
    ) {
      inputRef.current.focus();
    }
  }, [messages]);

  // Clear in-chat notification after a delay
  useEffect(() => {
    if (inChatNotification) {
      const timer = setTimeout(() => {
        setInChatNotification(null);
        setAnimateNotification(false); // Reset animation state
      }, 5000); // Clear after 5 seconds
      return () => clearTimeout(timer);
    }
  }, [inChatNotification]);

  // Initialize chat when opened
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      // Removed currentStep === ChatStep.WELCOME
      startConversation();
    }
  }, [isOpen, messages.length]);

  const startConversation = () => {
    clearBookingData(); // Clear any previous booking data
    setMessages([]); // Clear previous messages
    setCurrentStep(ChatStep.WELCOME); // Start with welcome
    messageIdCounter.current = 0; // Reset counter

    const flow = getChatFlow(ChatStep.WELCOME, {
      companyName: companyConfig.name,
    });

    const welcomeMessage: ChatMessage = {
      id: `${Date.now()}-${messageIdCounter.current++}`,
      type: "bot",
      content: flow.content || "", // Ensure content is not undefined
      timestamp: new Date(),
      options: flow.options,
      inputType: flow.inputType,
      placeholder: flow.placeholder,
      validation: flow.validation,
    };

    setMessages([welcomeMessage]);
  };

  const addBotMessage = (
    content: string,
    options?: string[],
    inputType?: ChatMessage["inputType"],
    placeholder?: string,
    validation?: (value: string) => string | null
  ) => {
    setIsTyping(true);

    setTimeout(() => {
      const message: ChatMessage = {
        id: `${Date.now()}-${messageIdCounter.current++}`,
        type: "bot",
        content,
        timestamp: new Date(),
        options,
        inputType,
        placeholder,
        validation,
      };

      setMessages((prev) => [...prev, message]);
      setIsTyping(false);
    }, 1000); // Simulate typing delay
  };

  const addUserMessage = (content: string) => {
    const message: ChatMessage = {
      id: `${Date.now()}-${messageIdCounter.current++}`,
      type: "user",
      content,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, message]);
    setInputValue("");
  };

  // Handle option selection
  const handleOptionSelect = (option: string) => {
    addUserMessage(option);
    processUserResponse(option);
  };

  // Handle text input submission
  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) {
      setInputValue(""); // Clear input even if empty
      return;
    }

    // Validate if validation function exists
    const currentFlow = getChatFlow(currentStep, {
      ...bookingData,
      selectedService,
      selectedSubService,
    });
    if (currentFlow.validation) {
      const error = currentFlow.validation(inputValue);
      if (error) {
        setInChatNotification({
          title: "Invalid Input",
          description: error,
          variant: "destructive",
        });
        setAnimateNotification(true);
        setInputValue(""); // Clear input on validation error
        return;
      }
    }

    addUserMessage(inputValue);
    processUserResponse(inputValue);
  };

  // Process user responses and advance conversation
  const processUserResponse = (response: string | any) => {
    // Update booking data based on current step
    const updatedData = { ...bookingData };

    // Natural conversation flow - one field at a time
    switch (currentStep) {
      case ChatStep.WELCOME:
        // Just proceed to next step
        break;
      case ChatStep.ZIP_CODE:
        updatedData.zipCode = response.trim();
        addBotMessage(`Got it! Your zip code is ${updatedData.zipCode}.`);
        break;
      case ChatStep.NAME:
        updatedData.name = response.trim();
        addBotMessage(`Nice to meet you, ${updatedData.name}!`);
        break;
      case ChatStep.EMAIL:
        updatedData.email = response.trim();
        addBotMessage(`Perfect! I have your email as ${updatedData.email}.`);
        break;
      case ChatStep.PHONE:
        updatedData.phone = response.trim();
        addBotMessage(
          `Great! I have your phone number as ${updatedData.phone}.`
        );
        break;
      case ChatStep.ADDRESS:
        updatedData.address = response.trim();
        addBotMessage(`Your service address is: ${updatedData.address}`);
        break;
      case ChatStep.SERVICE_TYPE:
        setSelectedService(response);
        updatedData.service = response;
        addBotMessage(`You selected ${updatedData.service}.`);
        break;
      case ChatStep.SERVICE_CATEGORY:
        setSelectedSubService(response);
        updatedData.service = `${selectedService} - ${response}`;
        addBotMessage(
          `Understood. You need ${response} for ${selectedService}.`
        );
        break;
      case ChatStep.SERVICE_SPECIFIC:
        updatedData.service = `${selectedService} - ${selectedSubService} - ${response}`;
        addBotMessage(`Okay, specifically ${response}.`);
        break;
      case ChatStep.SERVICE_DESCRIPTION:
        if (response === "Skip") {
          updatedData.description = ""; // Set description to empty if skipped
          addBotMessage(`Okay, skipping the description.`);
        } else {
          updatedData.description = response.trim();
          addBotMessage(`I've noted your description.`);
        }
        break;
      case ChatStep.SCHEDULE_DATE:
        const parts = response.split(", ");
        const dateString = `${parts[0]}, ${parts[1]} ${parts[2]}, ${parts[3]}`; // "Mon, Aug 28, 2025"
        const timeRange = parts[4]; // "08:00 - 10:00"

        updatedData.date = dateString; // Store as string
        updatedData.time = timeRange;
        addBotMessage(
          `You'd like to schedule for ${dateString} at ${timeRange}.`
        );
        break;
      case ChatStep.SCHEDULE_TIME:
        updatedData.time = response;
        addBotMessage(`And the best time is ${updatedData.time}.`);
        break;
      case ChatStep.CONFIRMATION:
        if (response.includes("book")) {
          if (!termsAccepted) {
            addBotMessage("Please accept the Terms and Conditions to proceed.");
            return; // Do not proceed if terms are not accepted
          }
          // Complete the booking
          handleBookingComplete(updatedData);
          return;
        } else {
          // Start over for changes
          setMessages([]);
          setCurrentStep(ChatStep.WELCOME);
          startConversation();
          return;
        }
      case ChatStep.COMPLETE:
        if (response.includes("another")) {
          // Start new booking
          clearBookingData();
          setMessages([]);
          setCurrentStep(ChatStep.WELCOME);
          startConversation();
          return;
        } else if (response.includes("That's all, thanks!")) {
          // Explicitly check for "That's all, thanks!"
          onComplete?.(bookingData); // Call onComplete here to pass data and close the parent component
          return;
        } else {
          // Default behavior if response is not "another" or "That's all, thanks!"
          // This might happen if there are other options or unexpected input
          onComplete?.(bookingData); // Call onComplete here as well
          return;
        }
    }

    // Update booking data
    updateBookingData(updatedData);

    // Get next step
    const nextStep = getNextStep(currentStep, response, {
      selectedService,
      selectedSubService,
      ...updatedData,
      response: response,
    });

    setCurrentStep(nextStep);

    // Add next bot message
    const flow = getChatFlow(nextStep, {
      companyName: companyConfig.name,
      selectedService:
        currentStep === ChatStep.SERVICE_TYPE ? response : selectedService,
      selectedSubService,
      ...updatedData,
    });

    // Special handling for Confirmation Step
    if (nextStep === ChatStep.CONFIRMATION) {
      flow.content = (
        <ChatConfirmationDisplay
          data={updatedData} // Pass the booking data
          onTermsAcceptedChange={setTermsAccepted} // Pass the setter for termsAccepted
        />
      );
    }

    addBotMessage(
      flow.content,
      flow.options,
      flow.inputType,
      flow.placeholder,
      flow.validation
    );
  };

  const handleBookingComplete = (data: any) => {
    setInChatNotification({
      title: "Booking Confirmed!",
      description:
        "Your appointment has been successfully scheduled. You'll receive a confirmation email shortly.",
    });
    setAnimateNotification(true);

    reward(); // Trigger confetti

    // Show completion message
    const flow = getChatFlow(ChatStep.COMPLETE, {
      companyName: companyConfig.name,
      ...data,
    });

    addBotMessage(flow.content, flow.options);
    setCurrentStep(ChatStep.COMPLETE);
  };

  return (
    <div
      className={cn(
        "fixed z-50 transition-all duration-300 ease-in-out",
        isOpen
          ? "bottom-4 right-4 opacity-100 translate-y-0"
          : "bottom-4 right-4 opacity-0 translate-y-4 pointer-events-none",
        className
      )}
    >
      <Card
        className={cn(
          "w-full max-w-md h-[600px] flex flex-col bg-white dark:bg-gray-800 shadow-2xl",
          className
        )}
      >
        <span id="chat-reward-id" className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-primary text-white rounded-t-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <img
                src={companyConfig.logo}
                alt={`${companyConfig.name} Logo`}
                className="w-8 h-8 object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                  e.currentTarget.nextElementSibling?.classList.remove(
                    "hidden"
                  );
                }}
              />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{companyConfig.name}</h3>
              <p className="text-xs opacity-90">Booking Assistant</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {" "}
            {/* New div for buttons */}
            <Button
              variant="ghost"
              size="icon"
              onClick={startConversation} // Call startConversation to redo
              className="text-white hover:bg-white/20 h-8 w-8"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/20 h-8 w-8"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {inChatNotification && (
          <div
            className={cn(
              "p-2 text-sm text-center transition-all duration-300 ease-out",
              inChatNotification.variant === "destructive"
                ? "bg-red-100 text-red-800"
                : "bg-green-100 text-green-800",
              animateNotification ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-full"
            )}
          >
            <p className="font-semibold">{inChatNotification.title}</p>
            <p>{inChatNotification.description}</p>
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <ChatMessageComponent
              key={message.id}
              message={message}
              onOptionSelect={handleOptionSelect}
            />
          ))}

          {/* Typing indicator */}
          {isTyping && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                <MessageCircle className="w-3 h-3 text-primary" />
              </div>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" />
                <div
                  className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                />
                <div
                  className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                />
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Always visible input area */}
        <form onSubmit={handleInputSubmit} className="p-4 border-t">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Type your message..."
              className="flex-1 min-w-0"
              autoFocus
            />
            <button
              type="submit"
              className="w-10 h-10 rounded-full p-0 flex-grow-0 flex-shrink-0 inline-flex items-center justify-center bg-primary text-primary-foreground hover:bg-primary/90 transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
              disabled={!inputValue.trim()}
            >
              <Send className="w-4 h-4" />
            </button>
          </div>
        </form>
      </Card>
    </div>
  );
}

// Floating Chat Button Component
export function FloatingChatButton({
  onBookingComplete,
  onBookingStart,
  className,
}: FloatingChatButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className={cn("fixed bottom-6 right-6 z-40", className)}>
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-14 h-14 rounded-full bg-gradient-primary hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 group p-0"
        >
          <MessageCircle className="w-6 h-6 group-hover:scale-110 transition-transform" />
        </Button>
      </div>

      <ChatPlugin
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
        }}
      />
    </>
  );
}
