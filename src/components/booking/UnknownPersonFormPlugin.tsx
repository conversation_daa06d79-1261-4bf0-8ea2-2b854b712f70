import { useState, useEffect } from "react";
import { User<PERSON><PERSON>, X, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import { UnknownPersonBooking } from "./UnknownPersonBooking";

interface UnknownPersonFormPluginProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
  className?: string;
}

interface FloatingUnknownPersonFormButtonProps {
  onBookingComplete?: (data: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

enum FormStep {
  PHONE_INPUT = "phoneInput",
  PERSONAL_INFO = "personalInfo",
  BOOKING_FORM = "bookingForm",
}

interface PersonalInfo {
  phone: string;
  name: string;
  email: string;
  address: string;
  zipCode: string;
}

export function UnknownPersonFormPlugin({
  isOpen,
  onClose,
  onComplete,
  className,
}: UnknownPersonFormPluginProps) {
  const companyConfig = useCompanyConfig();
  const { bookingData, updateBookingData, clearBookingData } =
    useBookingFormStorage();

  const [currentStep, setCurrentStep] = useState<FormStep>(
    FormStep.PHONE_INPUT
  );
  const [personalInfo, setPersonalInfo] = useState<PersonalInfo>({
    phone: "",
    name: "",
    email: "",
    address: "",
    zipCode: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(FormStep.PHONE_INPUT);
      setPersonalInfo({
        phone: "",
        name: "",
        email: "",
        address: "",
        zipCode: "",
      });
    }
  }, [isOpen]);

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!personalInfo.phone.trim()) return;

    if (!termsAccepted) {
      toast({
        title: "Terms Required",
        description: "Please accept the terms and conditions to proceed.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    setCurrentStep(FormStep.PERSONAL_INFO);
    setIsLoading(false);
  };

  const handlePersonalInfoSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (
      !personalInfo.name.trim() ||
      !personalInfo.email.trim() ||
      !personalInfo.address.trim()
    ) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Update booking data with personal info
    const userData = {
      phone: personalInfo.phone,
      name: personalInfo.name,
      firstName: personalInfo.name.split(" ")[0],
      lastName: personalInfo.name.split(" ").slice(1).join(" "),
      email: personalInfo.email,
      address: personalInfo.address,
      zipCode: personalInfo.zipCode,
    };

    updateBookingData(userData);
    setCurrentStep(FormStep.BOOKING_FORM);
  };

  const handleBookingComplete = (data: any) => {
    toast({
      title: "Booking Confirmed!",
      description: "Your appointment has been successfully scheduled.",
    });
    onComplete?.(data);
    onClose();
  };

  const updatePersonalInfo = (field: keyof PersonalInfo, value: string) => {
    setPersonalInfo((prev) => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
      <Card
        className={cn(
          "w-full max-w-4xl bg-white dark:bg-gray-800 shadow-2xl max-h-[90vh] overflow-hidden",
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-white text-gray-900 rounded-t-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <UserPlus className="w-4 h-4 text-gray-600" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{companyConfig.name}</h3>
              <p className="text-xs text-gray-500">New Customer Form</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 h-8 w-8"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {currentStep === FormStep.PHONE_INPUT && (
            <div className="space-y-4">
              <div className="text-center">
                <h2 className="text-xl font-semibold mb-2">Welcome!</h2>
                <p className="text-muted-foreground text-sm">
                  Let's start with your phone number
                </p>
              </div>

              <form onSubmit={handlePhoneSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={personalInfo.phone}
                    onChange={(e) =>
                      updatePersonalInfo("phone", e.target.value)
                    }
                    placeholder="Enter your phone number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                    required
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    checked={termsAccepted}
                    onCheckedChange={(checked) =>
                      setTermsAccepted(checked === true)
                    }
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    I accept the{" "}
                    <a href="#" className="underline text-gray-600">
                      terms and conditions
                    </a>
                  </label>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-gray-900 hover:bg-gray-800 text-white"
                  disabled={isLoading || !termsAccepted}
                >
                  {isLoading ? "Processing..." : "Continue"}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </form>
            </div>
          )}

          {currentStep === FormStep.PERSONAL_INFO && (
            <div className="space-y-4">
              <div className="text-center">
                <h2 className="text-xl font-semibold mb-2">Your Information</h2>
                <p className="text-muted-foreground text-sm">
                  Please provide your details for the booking
                </p>
              </div>

              <form onSubmit={handlePersonalInfoSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    value={personalInfo.name}
                    onChange={(e) => updatePersonalInfo("name", e.target.value)}
                    placeholder="Enter your full name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={personalInfo.email}
                    onChange={(e) =>
                      updatePersonalInfo("email", e.target.value)
                    }
                    placeholder="Enter your email address"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Address *
                  </label>
                  <input
                    type="text"
                    value={personalInfo.address}
                    onChange={(e) =>
                      updatePersonalInfo("address", e.target.value)
                    }
                    placeholder="Enter your complete address"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Zip Code
                  </label>
                  <input
                    type="text"
                    value={personalInfo.zipCode}
                    onChange={(e) =>
                      updatePersonalInfo("zipCode", e.target.value)
                    }
                    placeholder="Enter your zip code"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full bg-purple-600 hover:bg-purple-700"
                >
                  Continue
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </form>
            </div>
          )}

          {currentStep === FormStep.BOOKING_FORM && (
            <UnknownPersonBooking
              isOpen={true}
              onClose={() => setCurrentStep(FormStep.PERSONAL_INFO)}
              onComplete={() => handleBookingComplete(personalInfo)}
              initialData={personalInfo}
            />
          )}
        </div>
      </Card>
    </div>
  );
}

// Floating Button Component
export function FloatingUnknownPersonFormButton({
  onBookingComplete,
  onBookingStart,
  className,
}: FloatingUnknownPersonFormButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className={cn("fixed bottom-6 right-20 z-40", className)}>
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-16 h-16 rounded-full bg-gray-900 hover:bg-gray-800 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 group p-0"
        >
          <div className="flex flex-col items-center">
            <UserPlus className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span className="text-xs font-medium">New Form</span>
          </div>
        </Button>
      </div>

      <UnknownPersonFormPlugin
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
          setIsOpen(false);
        }}
      />
    </>
  );
}
