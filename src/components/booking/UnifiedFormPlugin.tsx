import { useState, useEffect } from "react";
import { User, X, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import { findUserByPhone } from "@/data/knownUsers";
import { KnownPersonBooking } from "./KnownPersonBooking";
import { UnknownPersonBooking } from "./UnknownPersonBooking";

enum FormStep {
  PHONE_INPUT = "phone_input",
  VERIFY_DETAILS = "verify_details",
  PERSONAL_INFO = "personal_info",
  ADDRESS_INFO = "address_info",
  SERVICE_SELECTION = "service_selection",
}

interface UnifiedFormPluginProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
  className?: string;
}

interface FloatingUnifiedFormButtonProps {
  onBookingComplete?: (data: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

export function UnifiedFormPlugin({
  isOpen,
  onClose,
  onComplete,
  className,
}: UnifiedFormPluginProps) {
  const companyConfig = useCompanyConfig();
  const { clearBookingData } = useBookingFormStorage();

  const [currentStep, setCurrentStep] = useState<FormStep>(
    FormStep.PHONE_INPUT
  );
  const [phoneNumber, setPhoneNumber] = useState("");
  const [foundUser, setFoundUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [personalInfo, setPersonalInfo] = useState({
    phone: "",
    name: "",
    email: "",
    address: "",
    city: "",
    zipCode: "",
    service: "",
  });

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(FormStep.PHONE_INPUT);
      setPhoneNumber("");
      setFoundUser(null);
      setTermsAccepted(false);
      setPersonalInfo({
        phone: "",
        name: "",
        email: "",
        address: "",
        city: "",
        zipCode: "",
        service: "",
      });
    }
  }, [isOpen]);

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!phoneNumber.trim()) return;

    if (!termsAccepted) {
      toast({
        title: "Terms Required",
        description: "Please accept the terms and conditions to proceed.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const user = findUserByPhone(phoneNumber);
      if (user) {
        setFoundUser(user);
        setCurrentStep(FormStep.VERIFY_DETAILS);
        toast({
          title: "User Found!",
          description: `Welcome back, ${user.name}!`,
        });
      } else {
        setPersonalInfo((prev) => ({ ...prev, phone: phoneNumber }));
        setCurrentStep(FormStep.PERSONAL_INFO);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to verify phone number. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePersonalInfoSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!personalInfo.name || !personalInfo.email) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }
    setCurrentStep(FormStep.ADDRESS_INFO);
  };

  const handleAddressInfoSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!personalInfo.address || !personalInfo.city || !personalInfo.zipCode) {
      toast({
        title: "Missing Information",
        description: "Please fill in all address fields.",
        variant: "destructive",
      });
      return;
    }
    setCurrentStep(FormStep.SERVICE_SELECTION);
  };

  const handleBookingComplete = (data: any) => {
    // Merge the booking data with personal info and found user data
    const completeData = {
      ...personalInfo,
      ...foundUser,
      ...data, // This contains service, date, time, etc. from booking components
    };

    onComplete?.(completeData);
    onClose();
    clearBookingData();
    setCurrentStep(FormStep.PHONE_INPUT);
    setPhoneNumber("");
    setTermsAccepted(false);
    setFoundUser(null);
    setPersonalInfo({
      phone: "",
      name: "",
      email: "",
      address: "",
      city: "",
      zipCode: "",
      service: "",
    });
  };

  const updatePersonalInfo = (field: string, value: string) => {
    setPersonalInfo((prev) => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div
      className={cn(
        "fixed inset-0 bg-gray-200 flex items-center justify-center p-4 z-50",
        !isOpen && "hidden"
      )}
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <Card
        className={cn(
          "w-full max-w-4xl bg-white dark:bg-gray-800 shadow-2xl max-h-[90vh] overflow-hidden",
          currentStep === FormStep.BOOKING_FORM ? "max-w-4xl" : "max-w-md",
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-purple-800 to-purple-900 text-white rounded-t-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <User className="w-4 h-4" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{companyConfig.name}</h3>
              <p className="text-xs opacity-90">Book Your Service</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-white hover:bg-white/20 h-8 w-8"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {currentStep === FormStep.PHONE_INPUT && (
            <form onSubmit={handlePhoneSubmit} className="space-y-4">
              <div className="text-center mb-6">
                <h2 className="text-xl font-semibold mb-2">Welcome!</h2>
                <p className="text-gray-600">
                  Let's start by getting your phone number to check if you're an
                  existing customer.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  placeholder="Enter your phone number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-800"
                  required
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="terms"
                  checked={termsAccepted}
                  onCheckedChange={(checked) =>
                    setTermsAccepted(checked === true)
                  }
                />
                <label
                  htmlFor="terms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I accept the{" "}
                  <a href="#" className="underline text-purple-800">
                    terms and conditions
                  </a>
                </label>
              </div>

              <Button
                type="submit"
                className="w-full bg-purple-800 hover:bg-purple-900 text-white"
                disabled={isLoading || !termsAccepted}
              >
                {isLoading ? "Checking..." : "Continue"}
              </Button>
            </form>
          )}

          {currentStep === FormStep.VERIFY_DETAILS && foundUser && (
            <div className="space-y-4">
              <div className="text-center mb-6">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-3" />
                <h2 className="text-xl font-semibold mb-2">Welcome Back!</h2>
                <p className="text-gray-600">
                  We found your information. Please confirm your details:
                </p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <div>
                  <strong>Name:</strong> {foundUser.name}
                </div>
                <div>
                  <strong>Email:</strong> {foundUser.email}
                </div>
                <div>
                  <strong>Phone:</strong> {foundUser.phone}
                </div>
                <div>
                  <strong>Address:</strong> {foundUser.address}
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={() => setCurrentStep(FormStep.SERVICE_SELECTION)}
                  className="flex-1 bg-purple-800 hover:bg-purple-900 text-white"
                >
                  Confirm & Continue
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(FormStep.PHONE_INPUT)}
                  className="flex-1"
                >
                  Update Info
                </Button>
              </div>
            </div>
          )}

          {currentStep === FormStep.PERSONAL_INFO && (
            <form onSubmit={handlePersonalInfoSubmit} className="space-y-4">
              <div className="text-center mb-6">
                <h2 className="text-xl font-semibold mb-2">Your Information</h2>
                <p className="text-gray-600">
                  Please provide your details to continue with the booking.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  value={personalInfo.name}
                  onChange={(e) => updatePersonalInfo("name", e.target.value)}
                  placeholder="Enter your full name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-800"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  value={personalInfo.email}
                  onChange={(e) => updatePersonalInfo("email", e.target.value)}
                  placeholder="Enter your email address"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-800"
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-purple-800 hover:bg-purple-900 text-white"
              >
                Continue
              </Button>
            </form>
          )}

          {currentStep === FormStep.ADDRESS_INFO && (
            <form onSubmit={handleAddressInfoSubmit} className="space-y-4">
              <div className="text-center mb-6">
                <h2 className="text-xl font-semibold mb-2">
                  Address Information
                </h2>
                <p className="text-gray-600">
                  Please provide your address details.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Street Address *
                </label>
                <input
                  type="text"
                  value={personalInfo.address}
                  onChange={(e) =>
                    updatePersonalInfo("address", e.target.value)
                  }
                  placeholder="Street name and house number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-800"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">City *</label>
                <input
                  type="text"
                  value={personalInfo.city || ""}
                  onChange={(e) => updatePersonalInfo("city", e.target.value)}
                  placeholder="Enter your city"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-800"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Zip Code *
                </label>
                <input
                  type="text"
                  value={personalInfo.zipCode}
                  onChange={(e) =>
                    updatePersonalInfo("zipCode", e.target.value)
                  }
                  placeholder="Enter your zip code"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-800"
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-purple-800 hover:bg-purple-900 text-white"
              >
                Continue
              </Button>
            </form>
          )}

          {currentStep === FormStep.SERVICE_SELECTION && foundUser && (
            <KnownPersonBooking
              isOpen={true}
              onClose={() => setCurrentStep(FormStep.VERIFY_DETAILS)}
              onComplete={() => handleBookingComplete(foundUser)}
              initialData={foundUser}
            />
          )}

          {currentStep === FormStep.SERVICE_SELECTION && !foundUser && (
            <UnknownPersonBooking
              isOpen={true}
              onClose={() => setCurrentStep(FormStep.ADDRESS_INFO)}
              onComplete={() => handleBookingComplete(personalInfo)}
              initialData={personalInfo}
            />
          )}
        </div>
      </Card>
    </div>
  );
}

// Floating Button Component
export function FloatingUnifiedFormButton({
  onBookingComplete,
  onBookingStart,
  className,
}: FloatingUnifiedFormButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className={cn("fixed bottom-6 right-6 z-40", className)}>
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-800 to-purple-900 hover:from-purple-900 hover:to-purple-800 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 group p-0"
        >
          <div className="flex flex-col items-center">
            <User className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span className="text-xs font-medium">Form</span>
          </div>
        </Button>
      </div>

      <UnifiedFormPlugin
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
          setIsOpen(false);
        }}
      />
    </>
  );
}
