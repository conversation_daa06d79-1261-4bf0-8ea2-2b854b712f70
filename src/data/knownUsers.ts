// Simulated database of known users
export interface KnownUser {
  phone: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  address: string;
  zipCode: string;
}

export const KNOWN_USERS: KnownUser[] = [
  {
    phone: "0987654321",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    address: "Rosenhof 12, 8002 Zurich",
    zipCode: "8002",
  },
  {
    phone: "******-0123",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    address: "123 Main Street, New York, NY 10001",
    zipCode: "10001",
  },
  {
    phone: "555-0456",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    address: "456 Oak Avenue, Los Angeles, CA 90210",
    zipCode: "90210",
  },
];

// Function to find user by phone number
export function findUserByPhone(phone: string): KnownUser | null {
  // Normalize phone number by removing spaces, dashes, and parentheses
  const normalizedPhone = phone.replace(/[\s\-\(\)]/g, "");
  
  return KNOWN_USERS.find(user => {
    const normalizedUserPhone = user.phone.replace(/[\s\-\(\)]/g, "");
    return normalizedUserPhone === normalizedPhone || 
           normalizedUserPhone.endsWith(normalizedPhone) ||
           normalizedPhone.endsWith(normalizedUserPhone);
  }) || null;
}

// Function to check if phone number exists in our system
export function isKnownUser(phone: string): boolean {
  return findUserByPhone(phone) !== null;
}
