@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 259 94% 51%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 259 94% 65%;
    --primary-dark: 259 94% 35%;

    --secondary: 260 80% 90%;
    --secondary-foreground: 259 94% 35%;

    --muted: 250 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 259 100% 95%;
    --accent-foreground: 259 94% 35%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 70%;
    --ring: 259 94% 51%;

    /* Custom booking form colors */
    --booking-bg: 250 20% 98%;
    --booking-card: 0 0% 100%;
    --booking-step: 259 94% 51%;
    --booking-step-complete: 142 71% 45%;
    --booking-step-inactive: 220 13% 91%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(259 94% 51%), hsl(259 94% 65%));
    --gradient-subtle: linear-gradient(180deg, hsl(250 20% 98%), hsl(0 0% 100%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(250 40% 98%));

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 259 94% 65%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --primary-light: 259 94% 75%;
    --primary-dark: 259 94% 45%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 259 94% 65%;

    /* Dark mode booking colors */
    --booking-bg: 222.2 84% 4.9%;
    --booking-card: 217.2 32.6% 17.5%;
    --booking-step: 259 94% 65%;
    --booking-step-complete: 142 71% 55%;
    --booking-step-inactive: 217.2 32.6% 25%;

    /* Dark gradients */
    --gradient-primary: linear-gradient(135deg, hsl(259 94% 65%), hsl(259 94% 75%));
    --gradient-subtle: linear-gradient(180deg, hsl(222.2 84% 4.9%), hsl(217.2 32.6% 17.5%));
    --gradient-card: linear-gradient(145deg, hsl(217.2 32.6% 17.5%), hsl(217.2 32.6% 20%));
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}