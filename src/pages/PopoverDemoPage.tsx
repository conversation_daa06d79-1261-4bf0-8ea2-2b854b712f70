import React, { useState } from "react";
import { Calendar, MessageCircle, User } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FloatingUnifiedFormButton } from "@/components/booking/UnifiedFormPlugin";
import { FloatingKnownUserChatButton } from "@/components/booking/KnownUserChatPlugin";
import { useCompanyConfig } from "@/config/company";

const PopoverDemoPage = () => {
  const companyConfig = useCompanyConfig();
  const [activeDemo, setActiveDemo] = useState<"form" | "chat">("form");

  const handleBookingComplete = (data: any) => {
    console.log("Booking completed:", data);
  };

  const handleBookingStart = () => {
    console.log("Booking started");
  };

  const demoConfigs = {
    form: {
      title: "Smart Form Plugin",
      description:
        "Adaptive form that detects known/unknown customers automatically",
      icon: User,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    chat: {
      title: "Smart Chat Plugin",
      description:
        "Conversational interface that adapts for known/unknown customers",
      icon: MessageCircle,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-700 rounded-full mb-6">
            <Calendar className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent mb-4">
            Smart Plugin Demo
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Experience our intelligent booking plugins that automatically adapt
            for known and unknown customers.
          </p>
        </div>

        {/* Navigation */}
        <div className="flex justify-center mb-8">
          <div className="flex flex-wrap justify-center gap-2 p-1 bg-white rounded-lg border">
            {Object.entries(demoConfigs).map(([key, config]) => (
              <Button
                key={key}
                variant={activeDemo === key ? "default" : "ghost"}
                onClick={() => setActiveDemo(key as any)}
                className={
                  activeDemo === key
                    ? "bg-gradient-to-r from-purple-600 to-purple-700 text-white"
                    : ""
                }
              >
                <config.icon className="w-4 h-4 mr-2" />
                {config.title}
              </Button>
            ))}
          </div>
        </div>

        {/* Demo Content */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {React.createElement(demoConfigs[activeDemo].icon, {
                  className: "w-5 h-5",
                })}
                {demoConfigs[activeDemo].title}
                <Badge variant="outline">Active</Badge>
              </CardTitle>
              <CardDescription>
                {demoConfigs[activeDemo].description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">How to Test:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="font-medium text-purple-700 mb-2">
                        Known Customer Test:
                      </h5>
                      <ul className="space-y-1 text-sm text-gray-600">
                        <li>
                          • Use phone:{" "}
                          <code className="bg-purple-100 px-2 py-1 rounded text-purple-700">
                            0987654321
                          </code>
                        </li>
                        <li>• Plugin auto-detects Thomas Berger</li>
                        <li>• Shows contact/address confirmation</li>
                        <li>• Proceeds to service selection</li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-medium text-purple-700 mb-2">
                        Unknown Customer Test:
                      </h5>
                      <ul className="space-y-1 text-sm text-gray-600">
                        <li>• Enter any other phone number</li>
                        <li>• Plugin guides through info collection</li>
                        <li>• Collects name, email, address</li>
                        <li>• Complete booking flow</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-purple-50 rounded-lg">
                  <h4 className="font-semibold mb-2">Sample Website Content</h4>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-lg">
                      {companyConfig.name} Services
                    </h3>
                    <p className="text-gray-600 text-sm">
                      We provide professional home services including plumbing,
                      electrical work, and heating maintenance. Our certified
                      technicians are available 24/7 to help with all your home
                      service needs.
                    </p>
                    <p className="text-sm text-purple-600 font-medium">
                      Look for the floating{" "}
                      {demoConfigs[activeDemo].title.toLowerCase()} button to
                      test the plugin!
                    </p>
                  </div>
                </div>

                <div className="bg-white border border-purple-200 rounded-lg p-4">
                  <h4 className="font-semibold mb-2 text-purple-700">
                    Key Features:
                  </h4>
                  <ul className="space-y-1 text-sm text-gray-600">
                    {activeDemo === "form" ? (
                      <>
                        <li>• Phone entry with terms acceptance</li>
                        <li>• Automatic customer detection</li>
                        <li>• Adaptive form fields</li>
                        <li>• Seamless booking flow</li>
                        <li>• Purple-themed interface</li>
                      </>
                    ) : (
                      <>
                        <li>• Natural conversation flow</li>
                        <li>• Intelligent customer recognition</li>
                        <li>• Context-aware responses</li>
                        <li>• Guided information collection</li>
                        <li>• Friendly chat experience</li>
                      </>
                    )}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Plugin Components */}
        {activeDemo === "form" && (
          <FloatingUnifiedFormButton
            onBookingComplete={handleBookingComplete}
            onBookingStart={handleBookingStart}
          />
        )}

        {activeDemo === "chat" && (
          <FloatingKnownUserChatButton
            onBookingComplete={handleBookingComplete}
            onBookingStart={handleBookingStart}
          />
        )}
      </div>
    </div>
  );
};

export default PopoverDemoPage;
